package com.quickride.customer.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.quickride.customer.data.model.User
import com.quickride.customer.data.repository.AuthRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class AuthViewModel(private val authRepository: AuthRepository) : ViewModel() {
    
    private val _uiState = MutableStateFlow(AuthUiState())
    val uiState: StateFlow<AuthUiState> = _uiState.asStateFlow()
    
    private val _currentUser = MutableStateFlow<User?>(null)
    val currentUser: StateFlow<User?> = _currentUser.asStateFlow()
    
    init {
        checkAuthState()
        observeAuthState()
    }
    
    /**
     * Check current authentication state
     */
    private fun checkAuthState() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val user = authRepository.getCurrentUser()
            _currentUser.value = user
            
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                isAuthenticated = user != null
            )
        }
    }
    
    /**
     * Observe authentication state changes
     */
    private fun observeAuthState() {
        viewModelScope.launch {
            authRepository.getCurrentUserFlow().collect { user ->
                _currentUser.value = user
                _uiState.value = _uiState.value.copy(
                    isAuthenticated = user != null
                )
            }
        }
    }
    
    /**
     * Sign up with email
     */
    fun signUpWithEmail(email: String, password: String, name: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            authRepository.signUpWithEmail(email, password)
                .onSuccess { message ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        message = message
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Sign up failed"
                    )
                }
        }
    }
    
    /**
     * Sign up with phone
     */
    fun signUpWithPhone(phone: String, name: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            authRepository.signUpWithPhone(phone)
                .onSuccess { message ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        showOtpScreen = true,
                        pendingPhone = phone,
                        pendingName = name,
                        message = message
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Failed to send OTP"
                    )
                }
        }
    }
    
    /**
     * Verify phone OTP for sign up
     */
    fun verifySignUpOTP(otp: String) {
        val phone = _uiState.value.pendingPhone ?: return
        val name = _uiState.value.pendingName ?: return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            authRepository.verifyPhoneOtp(phone, otp)
                .onSuccess { user ->
                    _currentUser.value = user
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isAuthenticated = true,
                        showOtpScreen = false,
                        message = "Account created successfully!"
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Invalid OTP"
                    )
                }
        }
    }
    
    /**
     * Sign in with email
     */
    fun signInWithEmail(email: String, password: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            authRepository.signInWithEmail(email, password)
                .onSuccess { user ->
                    _currentUser.value = user
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isAuthenticated = true,
                        message = "Welcome back!"
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Sign in failed"
                    )
                }
        }
    }
    
    /**
     * Sign in with phone
     */
    fun signInWithPhone(phone: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            authRepository.signInWithPhone(phone)
                .onSuccess { message ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        showOtpScreen = true,
                        pendingPhone = phone,
                        isSignIn = true,
                        message = message
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Failed to send OTP"
                    )
                }
        }
    }
    
    /**
     * Verify phone OTP for sign in
     */
    fun verifySignInOTP(otp: String) {
        val phone = _uiState.value.pendingPhone ?: return
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            authRepository.verifyPhoneOtp(phone, otp)
                .onSuccess { user ->
                    _currentUser.value = user
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isAuthenticated = true,
                        showOtpScreen = false,
                        message = "Welcome back!"
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Invalid OTP"
                    )
                }
        }
    }
    
    /**
     * Sign out
     */
    fun signOut() {
        viewModelScope.launch {
            authRepository.signOut()
                .onSuccess {
                    _currentUser.value = null
                    _uiState.value = AuthUiState() // Reset to initial state
                }
        }
    }
    
    /**
     * Reset password
     */
    fun resetPassword(email: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            authRepository.resetPassword(email)
                .onSuccess { message ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        message = message
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Failed to send reset email"
                    )
                }
        }
    }
    
    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * Clear success message
     */
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
    
    /**
     * Toggle between sign in and sign up
     */
    fun toggleAuthMode() {
        _uiState.value = _uiState.value.copy(
            isSignIn = !_uiState.value.isSignIn,
            error = null,
            message = null
        )
    }
    
    /**
     * Go back from OTP screen
     */
    fun goBackFromOtp() {
        _uiState.value = _uiState.value.copy(
            showOtpScreen = false,
            pendingPhone = null,
            pendingName = null,
            error = null
        )
    }
}

data class AuthUiState(
    val isLoading: Boolean = false,
    val isAuthenticated: Boolean = false,
    val isSignIn: Boolean = true,
    val showOtpScreen: Boolean = false,
    val pendingPhone: String? = null,
    val pendingName: String? = null,
    val error: String? = null,
    val message: String? = null
)
