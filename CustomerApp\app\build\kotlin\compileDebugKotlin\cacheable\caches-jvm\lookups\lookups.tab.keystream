  Activity android.app  Bundle android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  QuickRideCustomerTheme android.app.Activity  QuickRideNavigation android.app.Activity  Surface android.app.Activity  fillMaxSize android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  	setWindow android.app.Activity  window android.app.Activity  QuickRideApplication android.app.Application  SupabaseRepository android.app.Application  getValue android.app.Application  instance android.app.Application  lazy android.app.Application  onCreate android.app.Application  provideDelegate android.app.Application  Context android.content  Bundle android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  QuickRideApplication android.content.Context  QuickRideCustomerTheme android.content.Context  QuickRideNavigation android.content.Context  SupabaseRepository android.content.Context  Surface android.content.Context  fillMaxSize android.content.Context  getValue android.content.Context  instance android.content.Context  lazy android.content.Context  onCreate android.content.Context  provideDelegate android.content.Context  
setContent android.content.Context  Bundle android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  QuickRideApplication android.content.ContextWrapper  QuickRideCustomerTheme android.content.ContextWrapper  QuickRideNavigation android.content.ContextWrapper  SupabaseRepository android.content.ContextWrapper  Surface android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  getValue android.content.ContextWrapper  instance android.content.ContextWrapper  lazy android.content.ContextWrapper  onCreate android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  View android.view  Window android.view  Bundle  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  QuickRideCustomerTheme  android.view.ContextThemeWrapper  QuickRideNavigation  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  getISInEditMode android.view.View  getIsInEditMode android.view.View  isInEditMode android.view.View  
setContext android.view.View  
setInEditMode android.view.View  getSTATUSBarColor android.view.Window  getStatusBarColor android.view.Window  setStatusBarColor android.view.Window  statusBarColor android.view.Window  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  QuickRideCustomerTheme #androidx.activity.ComponentActivity  QuickRideNavigation #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  
HomeScreen /androidx.compose.animation.AnimatedContentScope  LoginScreen /androidx.compose.animation.AnimatedContentScope  isSystemInDarkTheme androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Button .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  Text +androidx.compose.foundation.layout.RowScope  Arrangement androidx.compose.material3  Button androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  Spacer androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  
background &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  headlineMedium %androidx.compose.material3.Typography  Arrangement androidx.compose.runtime  Button androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
SideEffect androidx.compose.runtime  Spacer androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  CenterHorizontally androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	getTOArgb "androidx.compose.ui.graphics.Color  	getToArgb "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  invoke ,androidx.compose.ui.graphics.Color.Companion  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  PasswordVisualTransformation androidx.compose.ui.text.input  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  QuickRideCustomerTheme #androidx.core.app.ComponentActivity  QuickRideNavigation #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  getISAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getIsAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  setAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  AuthRepository androidx.lifecycle.ViewModel  AuthUiState androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  User androidx.lifecycle.ViewModel  _currentUser androidx.lifecycle.ViewModel  _uiState androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  authRepository androidx.lifecycle.ViewModel  checkAuthState androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  observeAuthState androidx.lifecycle.ViewModel  	onFailure androidx.lifecycle.ViewModel  	onSuccess androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  MultiDexApplication androidx.multidex  QuickRideApplication %androidx.multidex.MultiDexApplication  SupabaseRepository %androidx.multidex.MultiDexApplication  getValue %androidx.multidex.MultiDexApplication  instance %androidx.multidex.MultiDexApplication  lazy %androidx.multidex.MultiDexApplication  onCreate %androidx.multidex.MultiDexApplication  provideDelegate %androidx.multidex.MultiDexApplication  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  navigate !androidx.navigation.NavController  
HomeScreen #androidx.navigation.NavGraphBuilder  LoginScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  
getCOMPOSABLE #androidx.navigation.NavGraphBuilder  
getComposable #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  invoke %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  	inclusive "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  MainActivity com.quickride.customer  
MaterialTheme com.quickride.customer  Modifier com.quickride.customer  QuickRideApplication com.quickride.customer  QuickRideCustomerTheme com.quickride.customer  QuickRideNavigation com.quickride.customer  SupabaseRepository com.quickride.customer  Surface com.quickride.customer  fillMaxSize com.quickride.customer  getValue com.quickride.customer  instance com.quickride.customer  lazy com.quickride.customer  provideDelegate com.quickride.customer  
setContent com.quickride.customer  Bundle #com.quickride.customer.MainActivity  
MaterialTheme #com.quickride.customer.MainActivity  Modifier #com.quickride.customer.MainActivity  QuickRideCustomerTheme #com.quickride.customer.MainActivity  QuickRideNavigation #com.quickride.customer.MainActivity  Surface #com.quickride.customer.MainActivity  fillMaxSize #com.quickride.customer.MainActivity  getFILLMaxSize #com.quickride.customer.MainActivity  getFillMaxSize #com.quickride.customer.MainActivity  
getSETContent #com.quickride.customer.MainActivity  
getSetContent #com.quickride.customer.MainActivity  
setContent #com.quickride.customer.MainActivity  QuickRideApplication +com.quickride.customer.QuickRideApplication  SupabaseRepository +com.quickride.customer.QuickRideApplication  getGETValue +com.quickride.customer.QuickRideApplication  getGetValue +com.quickride.customer.QuickRideApplication  getINSTANCE +com.quickride.customer.QuickRideApplication  getInstance +com.quickride.customer.QuickRideApplication  getLAZY +com.quickride.customer.QuickRideApplication  getLazy +com.quickride.customer.QuickRideApplication  getPROVIDEDelegate +com.quickride.customer.QuickRideApplication  getProvideDelegate +com.quickride.customer.QuickRideApplication  getValue +com.quickride.customer.QuickRideApplication  instance +com.quickride.customer.QuickRideApplication  lazy +com.quickride.customer.QuickRideApplication  provideDelegate +com.quickride.customer.QuickRideApplication  supabaseRepository +com.quickride.customer.QuickRideApplication  QuickRideApplication 5com.quickride.customer.QuickRideApplication.Companion  SupabaseRepository 5com.quickride.customer.QuickRideApplication.Companion  getGETValue 5com.quickride.customer.QuickRideApplication.Companion  getGetValue 5com.quickride.customer.QuickRideApplication.Companion  getLAZY 5com.quickride.customer.QuickRideApplication.Companion  getLazy 5com.quickride.customer.QuickRideApplication.Companion  getPROVIDEDelegate 5com.quickride.customer.QuickRideApplication.Companion  getProvideDelegate 5com.quickride.customer.QuickRideApplication.Companion  getValue 5com.quickride.customer.QuickRideApplication.Companion  instance 5com.quickride.customer.QuickRideApplication.Companion  lazy 5com.quickride.customer.QuickRideApplication.Companion  provideDelegate 5com.quickride.customer.QuickRideApplication.Companion  Boolean !com.quickride.customer.data.model  Double !com.quickride.customer.data.model  Driver !com.quickride.customer.data.model  DriverStatus !com.quickride.customer.data.model  Int !com.quickride.customer.data.model  Location !com.quickride.customer.data.model  NearbyDriver !com.quickride.customer.data.model  PaymentMode !com.quickride.customer.data.model  RideHistory !com.quickride.customer.data.model  RideRequest !com.quickride.customer.data.model  
RideStatus !com.quickride.customer.data.model  String !com.quickride.customer.data.model  User !com.quickride.customer.data.model  UserType !com.quickride.customer.data.model  Boolean (com.quickride.customer.data.model.Driver  Double (com.quickride.customer.data.model.Driver  DriverStatus (com.quickride.customer.data.model.Driver  Int (com.quickride.customer.data.model.Driver  Location (com.quickride.customer.data.model.Driver  String (com.quickride.customer.data.model.Driver  Boolean 2com.quickride.customer.data.model.Driver.Companion  Double 2com.quickride.customer.data.model.Driver.Companion  DriverStatus 2com.quickride.customer.data.model.Driver.Companion  Int 2com.quickride.customer.data.model.Driver.Companion  Location 2com.quickride.customer.data.model.Driver.Companion  String 2com.quickride.customer.data.model.Driver.Companion  OFFLINE .com.quickride.customer.data.model.DriverStatus  Double *com.quickride.customer.data.model.Location  Double 4com.quickride.customer.data.model.Location.Companion  Double .com.quickride.customer.data.model.NearbyDriver  String .com.quickride.customer.data.model.NearbyDriver  Double 8com.quickride.customer.data.model.NearbyDriver.Companion  String 8com.quickride.customer.data.model.NearbyDriver.Companion  Double -com.quickride.customer.data.model.RideHistory  Int -com.quickride.customer.data.model.RideHistory  PaymentMode -com.quickride.customer.data.model.RideHistory  String -com.quickride.customer.data.model.RideHistory  Double 7com.quickride.customer.data.model.RideHistory.Companion  Int 7com.quickride.customer.data.model.RideHistory.Companion  PaymentMode 7com.quickride.customer.data.model.RideHistory.Companion  String 7com.quickride.customer.data.model.RideHistory.Companion  Double -com.quickride.customer.data.model.RideRequest  Int -com.quickride.customer.data.model.RideRequest  Location -com.quickride.customer.data.model.RideRequest  PaymentMode -com.quickride.customer.data.model.RideRequest  
RideStatus -com.quickride.customer.data.model.RideRequest  String -com.quickride.customer.data.model.RideRequest  Double 7com.quickride.customer.data.model.RideRequest.Companion  Int 7com.quickride.customer.data.model.RideRequest.Companion  Location 7com.quickride.customer.data.model.RideRequest.Companion  PaymentMode 7com.quickride.customer.data.model.RideRequest.Companion  
RideStatus 7com.quickride.customer.data.model.RideRequest.Companion  String 7com.quickride.customer.data.model.RideRequest.Companion  PENDING ,com.quickride.customer.data.model.RideStatus  Boolean &com.quickride.customer.data.model.User  String &com.quickride.customer.data.model.User  equals &com.quickride.customer.data.model.User  Boolean 0com.quickride.customer.data.model.User.Companion  String 0com.quickride.customer.data.model.User.Companion  invoke 0com.quickride.customer.data.model.User.Companion  AuthRepository &com.quickride.customer.data.repository  Double &com.quickride.customer.data.repository  	Exception &com.quickride.customer.data.repository  List &com.quickride.customer.data.repository  Map &com.quickride.customer.data.repository  RealtimeRepository &com.quickride.customer.data.repository  Result &com.quickride.customer.data.repository  String &com.quickride.customer.data.repository  SupabaseRepository &com.quickride.customer.data.repository  System &com.quickride.customer.data.repository  Unit &com.quickride.customer.data.repository  User &com.quickride.customer.data.repository  	emptyList &com.quickride.customer.data.repository  flow &com.quickride.customer.data.repository  	Exception 5com.quickride.customer.data.repository.AuthRepository  Flow 5com.quickride.customer.data.repository.AuthRepository  Result 5com.quickride.customer.data.repository.AuthRepository  String 5com.quickride.customer.data.repository.AuthRepository  SupabaseRepository 5com.quickride.customer.data.repository.AuthRepository  System 5com.quickride.customer.data.repository.AuthRepository  Unit 5com.quickride.customer.data.repository.AuthRepository  User 5com.quickride.customer.data.repository.AuthRepository  flow 5com.quickride.customer.data.repository.AuthRepository  getCurrentUser 5com.quickride.customer.data.repository.AuthRepository  getCurrentUserFlow 5com.quickride.customer.data.repository.AuthRepository  getFLOW 5com.quickride.customer.data.repository.AuthRepository  getFlow 5com.quickride.customer.data.repository.AuthRepository  invoke 5com.quickride.customer.data.repository.AuthRepository  
resetPassword 5com.quickride.customer.data.repository.AuthRepository  signInWithEmail 5com.quickride.customer.data.repository.AuthRepository  signInWithPhone 5com.quickride.customer.data.repository.AuthRepository  signOut 5com.quickride.customer.data.repository.AuthRepository  signUpWithEmail 5com.quickride.customer.data.repository.AuthRepository  signUpWithPhone 5com.quickride.customer.data.repository.AuthRepository  verifyPhoneOtp 5com.quickride.customer.data.repository.AuthRepository  Double 9com.quickride.customer.data.repository.RealtimeRepository  Flow 9com.quickride.customer.data.repository.RealtimeRepository  List 9com.quickride.customer.data.repository.RealtimeRepository  Map 9com.quickride.customer.data.repository.RealtimeRepository  NearbyDriver 9com.quickride.customer.data.repository.RealtimeRepository  RideRequest 9com.quickride.customer.data.repository.RealtimeRepository  String 9com.quickride.customer.data.repository.RealtimeRepository  SupabaseRepository 9com.quickride.customer.data.repository.RealtimeRepository  	emptyList 9com.quickride.customer.data.repository.RealtimeRepository  flow 9com.quickride.customer.data.repository.RealtimeRepository  getEMPTYList 9com.quickride.customer.data.repository.RealtimeRepository  getEmptyList 9com.quickride.customer.data.repository.RealtimeRepository  getFLOW 9com.quickride.customer.data.repository.RealtimeRepository  getFlow 9com.quickride.customer.data.repository.RealtimeRepository  
initialize 9com.quickride.customer.data.repository.SupabaseRepository  
HomeScreen $com.quickride.customer.ui.navigation  LoginScreen $com.quickride.customer.ui.navigation  QuickRideNavigation $com.quickride.customer.ui.navigation  
composable $com.quickride.customer.ui.navigation  Arrangement &com.quickride.customer.ui.screens.auth  Button &com.quickride.customer.ui.screens.auth  Column &com.quickride.customer.ui.screens.auth  
Composable &com.quickride.customer.ui.screens.auth  ExperimentalMaterial3Api &com.quickride.customer.ui.screens.auth  LoginScreen &com.quickride.customer.ui.screens.auth  
MaterialTheme &com.quickride.customer.ui.screens.auth  Modifier &com.quickride.customer.ui.screens.auth  OptIn &com.quickride.customer.ui.screens.auth  OutlinedTextField &com.quickride.customer.ui.screens.auth  PasswordVisualTransformation &com.quickride.customer.ui.screens.auth  Spacer &com.quickride.customer.ui.screens.auth  Text &com.quickride.customer.ui.screens.auth  
TextButton &com.quickride.customer.ui.screens.auth  Unit &com.quickride.customer.ui.screens.auth  fillMaxSize &com.quickride.customer.ui.screens.auth  fillMaxWidth &com.quickride.customer.ui.screens.auth  getValue &com.quickride.customer.ui.screens.auth  height &com.quickride.customer.ui.screens.auth  mutableStateOf &com.quickride.customer.ui.screens.auth  padding &com.quickride.customer.ui.screens.auth  provideDelegate &com.quickride.customer.ui.screens.auth  remember &com.quickride.customer.ui.screens.auth  setValue &com.quickride.customer.ui.screens.auth  Arrangement &com.quickride.customer.ui.screens.home  Button &com.quickride.customer.ui.screens.home  Column &com.quickride.customer.ui.screens.home  
Composable &com.quickride.customer.ui.screens.home  
HomeScreen &com.quickride.customer.ui.screens.home  
MaterialTheme &com.quickride.customer.ui.screens.home  Modifier &com.quickride.customer.ui.screens.home  OutlinedButton &com.quickride.customer.ui.screens.home  Spacer &com.quickride.customer.ui.screens.home  Text &com.quickride.customer.ui.screens.home  Unit &com.quickride.customer.ui.screens.home  fillMaxSize &com.quickride.customer.ui.screens.home  fillMaxWidth &com.quickride.customer.ui.screens.home  height &com.quickride.customer.ui.screens.home  padding &com.quickride.customer.ui.screens.home  Boolean com.quickride.customer.ui.theme  Build com.quickride.customer.ui.theme  DarkColorScheme com.quickride.customer.ui.theme  DriverMarker com.quickride.customer.ui.theme  
DropMarker com.quickride.customer.ui.theme  Error com.quickride.customer.ui.theme  Info com.quickride.customer.ui.theme  LightColorScheme com.quickride.customer.ui.theme  PickupMarker com.quickride.customer.ui.theme  Pink40 com.quickride.customer.ui.theme  Pink80 com.quickride.customer.ui.theme  Primary com.quickride.customer.ui.theme  PrimaryVariant com.quickride.customer.ui.theme  Purple40 com.quickride.customer.ui.theme  Purple80 com.quickride.customer.ui.theme  PurpleGrey40 com.quickride.customer.ui.theme  PurpleGrey80 com.quickride.customer.ui.theme  QuickRideCustomerTheme com.quickride.customer.ui.theme  RideAccepted com.quickride.customer.ui.theme  
RideCancelled com.quickride.customer.ui.theme  
RideCompleted com.quickride.customer.ui.theme  RideOngoing com.quickride.customer.ui.theme  RidePending com.quickride.customer.ui.theme  
RouteColor com.quickride.customer.ui.theme  	Secondary com.quickride.customer.ui.theme  SecondaryVariant com.quickride.customer.ui.theme  Success com.quickride.customer.ui.theme  
Typography com.quickride.customer.ui.theme  Unit com.quickride.customer.ui.theme  Warning com.quickride.customer.ui.theme  WindowCompat com.quickride.customer.ui.theme  	Constants com.quickride.customer.utils  AuthUiState  com.quickride.customer.viewmodel  
AuthViewModel  com.quickride.customer.viewmodel  Boolean  com.quickride.customer.viewmodel  MutableStateFlow  com.quickride.customer.viewmodel  String  com.quickride.customer.viewmodel  _currentUser  com.quickride.customer.viewmodel  _uiState  com.quickride.customer.viewmodel  asStateFlow  com.quickride.customer.viewmodel  authRepository  com.quickride.customer.viewmodel  launch  com.quickride.customer.viewmodel  	onFailure  com.quickride.customer.viewmodel  	onSuccess  com.quickride.customer.viewmodel  viewModelScope  com.quickride.customer.viewmodel  Boolean ,com.quickride.customer.viewmodel.AuthUiState  String ,com.quickride.customer.viewmodel.AuthUiState  copy ,com.quickride.customer.viewmodel.AuthUiState  isSignIn ,com.quickride.customer.viewmodel.AuthUiState  pendingName ,com.quickride.customer.viewmodel.AuthUiState  pendingPhone ,com.quickride.customer.viewmodel.AuthUiState  AuthRepository .com.quickride.customer.viewmodel.AuthViewModel  AuthUiState .com.quickride.customer.viewmodel.AuthViewModel  MutableStateFlow .com.quickride.customer.viewmodel.AuthViewModel  	StateFlow .com.quickride.customer.viewmodel.AuthViewModel  String .com.quickride.customer.viewmodel.AuthViewModel  User .com.quickride.customer.viewmodel.AuthViewModel  _currentUser .com.quickride.customer.viewmodel.AuthViewModel  _uiState .com.quickride.customer.viewmodel.AuthViewModel  asStateFlow .com.quickride.customer.viewmodel.AuthViewModel  authRepository .com.quickride.customer.viewmodel.AuthViewModel  checkAuthState .com.quickride.customer.viewmodel.AuthViewModel  getASStateFlow .com.quickride.customer.viewmodel.AuthViewModel  getAsStateFlow .com.quickride.customer.viewmodel.AuthViewModel  	getLAUNCH .com.quickride.customer.viewmodel.AuthViewModel  	getLaunch .com.quickride.customer.viewmodel.AuthViewModel  getONFailure .com.quickride.customer.viewmodel.AuthViewModel  getONSuccess .com.quickride.customer.viewmodel.AuthViewModel  getOnFailure .com.quickride.customer.viewmodel.AuthViewModel  getOnSuccess .com.quickride.customer.viewmodel.AuthViewModel  getVIEWModelScope .com.quickride.customer.viewmodel.AuthViewModel  getViewModelScope .com.quickride.customer.viewmodel.AuthViewModel  launch .com.quickride.customer.viewmodel.AuthViewModel  observeAuthState .com.quickride.customer.viewmodel.AuthViewModel  	onFailure .com.quickride.customer.viewmodel.AuthViewModel  	onSuccess .com.quickride.customer.viewmodel.AuthViewModel  viewModelScope .com.quickride.customer.viewmodel.AuthViewModel  AuthUiState 	java.lang  Build 	java.lang  Button 	java.lang  DriverStatus 	java.lang  	Exception 	java.lang  ExperimentalMaterial3Api 	java.lang  
HomeScreen 	java.lang  LoginScreen 	java.lang  
MaterialTheme 	java.lang  Modifier 	java.lang  MutableStateFlow 	java.lang  OutlinedButton 	java.lang  OutlinedTextField 	java.lang  PasswordVisualTransformation 	java.lang  QuickRideCustomerTheme 	java.lang  QuickRideNavigation 	java.lang  Result 	java.lang  
RideStatus 	java.lang  Spacer 	java.lang  SupabaseRepository 	java.lang  Surface 	java.lang  System 	java.lang  Text 	java.lang  
TextButton 	java.lang  Unit 	java.lang  User 	java.lang  WindowCompat 	java.lang  _currentUser 	java.lang  _uiState 	java.lang  asStateFlow 	java.lang  authRepository 	java.lang  	emptyList 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  flow 	java.lang  getValue 	java.lang  height 	java.lang  instance 	java.lang  launch 	java.lang  lazy 	java.lang  	onFailure 	java.lang  	onSuccess 	java.lang  padding 	java.lang  provideDelegate 	java.lang  currentTimeMillis java.lang.System  AuthUiState kotlin  Boolean kotlin  Build kotlin  Button kotlin  Double kotlin  DriverStatus kotlin  	Exception kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  
HomeScreen kotlin  Int kotlin  Lazy kotlin  LoginScreen kotlin  Long kotlin  
MaterialTheme kotlin  Modifier kotlin  MutableStateFlow kotlin  Nothing kotlin  OptIn kotlin  OutlinedButton kotlin  OutlinedTextField kotlin  PasswordVisualTransformation kotlin  QuickRideCustomerTheme kotlin  QuickRideNavigation kotlin  Result kotlin  
RideStatus kotlin  Spacer kotlin  String kotlin  SupabaseRepository kotlin  Surface kotlin  System kotlin  Text kotlin  
TextButton kotlin  	Throwable kotlin  Unit kotlin  User kotlin  WindowCompat kotlin  _currentUser kotlin  _uiState kotlin  asStateFlow kotlin  authRepository kotlin  	emptyList kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  flow kotlin  getValue kotlin  height kotlin  instance kotlin  launch kotlin  lazy kotlin  	onFailure kotlin  	onSuccess kotlin  padding kotlin  provideDelegate kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  failure 
kotlin.Result  getONFailure 
kotlin.Result  getONSuccess 
kotlin.Result  getOnFailure 
kotlin.Result  getOnSuccess 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  AuthUiState kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  DriverStatus kotlin.annotation  	Exception kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  
HomeScreen kotlin.annotation  LoginScreen kotlin.annotation  
MaterialTheme kotlin.annotation  Modifier kotlin.annotation  MutableStateFlow kotlin.annotation  OutlinedButton kotlin.annotation  OutlinedTextField kotlin.annotation  PasswordVisualTransformation kotlin.annotation  QuickRideCustomerTheme kotlin.annotation  QuickRideNavigation kotlin.annotation  Result kotlin.annotation  
RideStatus kotlin.annotation  Spacer kotlin.annotation  SupabaseRepository kotlin.annotation  Surface kotlin.annotation  System kotlin.annotation  Text kotlin.annotation  
TextButton kotlin.annotation  Unit kotlin.annotation  User kotlin.annotation  WindowCompat kotlin.annotation  _currentUser kotlin.annotation  _uiState kotlin.annotation  asStateFlow kotlin.annotation  authRepository kotlin.annotation  	emptyList kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  flow kotlin.annotation  getValue kotlin.annotation  height kotlin.annotation  instance kotlin.annotation  launch kotlin.annotation  lazy kotlin.annotation  	onFailure kotlin.annotation  	onSuccess kotlin.annotation  padding kotlin.annotation  provideDelegate kotlin.annotation  AuthUiState kotlin.collections  Build kotlin.collections  Button kotlin.collections  DriverStatus kotlin.collections  	Exception kotlin.collections  ExperimentalMaterial3Api kotlin.collections  
HomeScreen kotlin.collections  List kotlin.collections  LoginScreen kotlin.collections  Map kotlin.collections  
MaterialTheme kotlin.collections  Modifier kotlin.collections  MutableStateFlow kotlin.collections  OutlinedButton kotlin.collections  OutlinedTextField kotlin.collections  PasswordVisualTransformation kotlin.collections  QuickRideCustomerTheme kotlin.collections  QuickRideNavigation kotlin.collections  Result kotlin.collections  
RideStatus kotlin.collections  Spacer kotlin.collections  SupabaseRepository kotlin.collections  Surface kotlin.collections  System kotlin.collections  Text kotlin.collections  
TextButton kotlin.collections  Unit kotlin.collections  User kotlin.collections  WindowCompat kotlin.collections  _currentUser kotlin.collections  _uiState kotlin.collections  asStateFlow kotlin.collections  authRepository kotlin.collections  	emptyList kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  flow kotlin.collections  getValue kotlin.collections  height kotlin.collections  instance kotlin.collections  launch kotlin.collections  lazy kotlin.collections  	onFailure kotlin.collections  	onSuccess kotlin.collections  padding kotlin.collections  provideDelegate kotlin.collections  AuthUiState kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  DriverStatus kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  
HomeScreen kotlin.comparisons  LoginScreen kotlin.comparisons  
MaterialTheme kotlin.comparisons  Modifier kotlin.comparisons  MutableStateFlow kotlin.comparisons  OutlinedButton kotlin.comparisons  OutlinedTextField kotlin.comparisons  PasswordVisualTransformation kotlin.comparisons  QuickRideCustomerTheme kotlin.comparisons  QuickRideNavigation kotlin.comparisons  Result kotlin.comparisons  
RideStatus kotlin.comparisons  Spacer kotlin.comparisons  SupabaseRepository kotlin.comparisons  Surface kotlin.comparisons  System kotlin.comparisons  Text kotlin.comparisons  
TextButton kotlin.comparisons  Unit kotlin.comparisons  User kotlin.comparisons  WindowCompat kotlin.comparisons  _currentUser kotlin.comparisons  _uiState kotlin.comparisons  asStateFlow kotlin.comparisons  authRepository kotlin.comparisons  	emptyList kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  flow kotlin.comparisons  getValue kotlin.comparisons  height kotlin.comparisons  instance kotlin.comparisons  launch kotlin.comparisons  lazy kotlin.comparisons  	onFailure kotlin.comparisons  	onSuccess kotlin.comparisons  padding kotlin.comparisons  provideDelegate kotlin.comparisons  SuspendFunction1 kotlin.coroutines  AuthUiState 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  DriverStatus 	kotlin.io  	Exception 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  
HomeScreen 	kotlin.io  LoginScreen 	kotlin.io  
MaterialTheme 	kotlin.io  Modifier 	kotlin.io  MutableStateFlow 	kotlin.io  OutlinedButton 	kotlin.io  OutlinedTextField 	kotlin.io  PasswordVisualTransformation 	kotlin.io  QuickRideCustomerTheme 	kotlin.io  QuickRideNavigation 	kotlin.io  Result 	kotlin.io  
RideStatus 	kotlin.io  Spacer 	kotlin.io  SupabaseRepository 	kotlin.io  Surface 	kotlin.io  System 	kotlin.io  Text 	kotlin.io  
TextButton 	kotlin.io  Unit 	kotlin.io  User 	kotlin.io  WindowCompat 	kotlin.io  _currentUser 	kotlin.io  _uiState 	kotlin.io  asStateFlow 	kotlin.io  authRepository 	kotlin.io  	emptyList 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  flow 	kotlin.io  getValue 	kotlin.io  height 	kotlin.io  instance 	kotlin.io  launch 	kotlin.io  lazy 	kotlin.io  	onFailure 	kotlin.io  	onSuccess 	kotlin.io  padding 	kotlin.io  provideDelegate 	kotlin.io  AuthUiState 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  DriverStatus 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  
HomeScreen 
kotlin.jvm  LoginScreen 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Modifier 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OutlinedButton 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  PasswordVisualTransformation 
kotlin.jvm  QuickRideCustomerTheme 
kotlin.jvm  QuickRideNavigation 
kotlin.jvm  Result 
kotlin.jvm  
RideStatus 
kotlin.jvm  Spacer 
kotlin.jvm  SupabaseRepository 
kotlin.jvm  Surface 
kotlin.jvm  System 
kotlin.jvm  Text 
kotlin.jvm  
TextButton 
kotlin.jvm  Unit 
kotlin.jvm  User 
kotlin.jvm  WindowCompat 
kotlin.jvm  _currentUser 
kotlin.jvm  _uiState 
kotlin.jvm  asStateFlow 
kotlin.jvm  authRepository 
kotlin.jvm  	emptyList 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  flow 
kotlin.jvm  getValue 
kotlin.jvm  height 
kotlin.jvm  instance 
kotlin.jvm  launch 
kotlin.jvm  lazy 
kotlin.jvm  	onFailure 
kotlin.jvm  	onSuccess 
kotlin.jvm  padding 
kotlin.jvm  provideDelegate 
kotlin.jvm  AuthUiState 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  DriverStatus 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  
HomeScreen 
kotlin.ranges  LoginScreen 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Modifier 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OutlinedButton 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  PasswordVisualTransformation 
kotlin.ranges  QuickRideCustomerTheme 
kotlin.ranges  QuickRideNavigation 
kotlin.ranges  Result 
kotlin.ranges  
RideStatus 
kotlin.ranges  Spacer 
kotlin.ranges  SupabaseRepository 
kotlin.ranges  Surface 
kotlin.ranges  System 
kotlin.ranges  Text 
kotlin.ranges  
TextButton 
kotlin.ranges  Unit 
kotlin.ranges  User 
kotlin.ranges  WindowCompat 
kotlin.ranges  _currentUser 
kotlin.ranges  _uiState 
kotlin.ranges  asStateFlow 
kotlin.ranges  authRepository 
kotlin.ranges  	emptyList 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  flow 
kotlin.ranges  getValue 
kotlin.ranges  height 
kotlin.ranges  instance 
kotlin.ranges  launch 
kotlin.ranges  lazy 
kotlin.ranges  	onFailure 
kotlin.ranges  	onSuccess 
kotlin.ranges  padding 
kotlin.ranges  provideDelegate 
kotlin.ranges  KClass kotlin.reflect  AuthUiState kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  DriverStatus kotlin.sequences  	Exception kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  
HomeScreen kotlin.sequences  LoginScreen kotlin.sequences  
MaterialTheme kotlin.sequences  Modifier kotlin.sequences  MutableStateFlow kotlin.sequences  OutlinedButton kotlin.sequences  OutlinedTextField kotlin.sequences  PasswordVisualTransformation kotlin.sequences  QuickRideCustomerTheme kotlin.sequences  QuickRideNavigation kotlin.sequences  Result kotlin.sequences  
RideStatus kotlin.sequences  Spacer kotlin.sequences  SupabaseRepository kotlin.sequences  Surface kotlin.sequences  System kotlin.sequences  Text kotlin.sequences  
TextButton kotlin.sequences  Unit kotlin.sequences  User kotlin.sequences  WindowCompat kotlin.sequences  _currentUser kotlin.sequences  _uiState kotlin.sequences  asStateFlow kotlin.sequences  authRepository kotlin.sequences  	emptyList kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  flow kotlin.sequences  getValue kotlin.sequences  height kotlin.sequences  instance kotlin.sequences  launch kotlin.sequences  lazy kotlin.sequences  	onFailure kotlin.sequences  	onSuccess kotlin.sequences  padding kotlin.sequences  provideDelegate kotlin.sequences  AuthUiState kotlin.text  Build kotlin.text  Button kotlin.text  DriverStatus kotlin.text  	Exception kotlin.text  ExperimentalMaterial3Api kotlin.text  
HomeScreen kotlin.text  LoginScreen kotlin.text  
MaterialTheme kotlin.text  Modifier kotlin.text  MutableStateFlow kotlin.text  OutlinedButton kotlin.text  OutlinedTextField kotlin.text  PasswordVisualTransformation kotlin.text  QuickRideCustomerTheme kotlin.text  QuickRideNavigation kotlin.text  Result kotlin.text  
RideStatus kotlin.text  Spacer kotlin.text  SupabaseRepository kotlin.text  Surface kotlin.text  System kotlin.text  Text kotlin.text  
TextButton kotlin.text  Unit kotlin.text  User kotlin.text  WindowCompat kotlin.text  _currentUser kotlin.text  _uiState kotlin.text  asStateFlow kotlin.text  authRepository kotlin.text  	emptyList kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  flow kotlin.text  getValue kotlin.text  height kotlin.text  instance kotlin.text  launch kotlin.text  lazy kotlin.text  	onFailure kotlin.text  	onSuccess kotlin.text  padding kotlin.text  provideDelegate kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  AuthUiState !kotlinx.coroutines.CoroutineScope  _currentUser !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  authRepository !kotlinx.coroutines.CoroutineScope  getAUTHRepository !kotlinx.coroutines.CoroutineScope  getAuthRepository !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getONFailure !kotlinx.coroutines.CoroutineScope  getONSuccess !kotlinx.coroutines.CoroutineScope  getOnFailure !kotlinx.coroutines.CoroutineScope  getOnSuccess !kotlinx.coroutines.CoroutineScope  get_currentUser !kotlinx.coroutines.CoroutineScope  get_uiState !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  emit %kotlinx.coroutines.flow.FlowCollector  	emptyList %kotlinx.coroutines.flow.FlowCollector  getEMPTYList %kotlinx.coroutines.flow.FlowCollector  getEmptyList %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  Serializable kotlinx.serialization                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     