package com.quickride.driver.data.model

import kotlinx.serialization.Serializable

@Serializable
data class Driver(
    val id: String,
    val name: String,
    val vehicleNo: String,
    val vehicleModel: String? = null,
    val vehicleColor: String? = null,
    val licenseNumber: String,
    val location: Location? = null,
    val status: DriverStatus = DriverStatus.OFFLINE,
    val rating: Double = 0.0,
    val totalTrips: Int = 0,
    val isVerified: Boolean = false,
    val createdAt: String,
    val updatedAt: String
)

@Serializable
data class DriverLocation(
    val id: String,
    val driverId: String,
    val location: Location,
    val heading: Double? = null,
    val speedKmh: Double? = null,
    val accuracyMeters: Double? = null,
    val timestamp: String
)

@Serializable
data class Location(
    val latitude: Double,
    val longitude: Double
)

@Serializable
data class DriverEarnings(
    val todayEarnings: Double = 0.0,
    val weeklyEarnings: Double = 0.0,
    val monthlyEarnings: Double = 0.0,
    val totalEarnings: Double = 0.0,
    val todayTrips: Int = 0,
    val weeklyTrips: Int = 0,
    val monthlyTrips: Int = 0,
    val totalTrips: Int = 0,
    val averageRating: Double = 0.0,
    val lastUpdated: String
)

@Serializable
data class VehicleInfo(
    val vehicleNo: String,
    val vehicleModel: String,
    val vehicleColor: String,
    val licenseNumber: String,
    val vehiclePhotoUrl: String? = null,
    val licensePhotoUrl: String? = null,
    val isVerified: Boolean = false
)

enum class DriverStatus {
    AVAILABLE, BUSY, OFFLINE
}
