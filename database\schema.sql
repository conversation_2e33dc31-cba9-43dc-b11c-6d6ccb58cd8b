-- QuickRide Database Schema
-- Run this script in Supabase SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Create custom types
CREATE TYPE user_type_enum AS ENUM ('customer', 'driver');
CREATE TYPE driver_status_enum AS ENUM ('available', 'busy', 'offline');
CREATE TYPE ride_status_enum AS ENUM ('pending', 'accepted', 'ongoing', 'completed', 'cancelled');
CREATE TYPE payment_mode_enum AS ENUM ('cash', 'upi', 'card', 'wallet');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    name TEXT NOT NULL,
    phone TEXT UNIQUE NOT NULL,
    user_type user_type_enum NOT NULL,
    profile_image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Drivers table (additional info for drivers)
CREATE TABLE public.drivers (
    id UUID REFERENCES public.users(id) ON DELETE CASCADE PRIMARY KEY,
    name TEXT NOT NULL,
    vehicle_no TEXT UNIQUE NOT NULL,
    vehicle_model TEXT,
    vehicle_color TEXT,
    license_number TEXT UNIQUE NOT NULL,
    location GEOGRAPHY(POINT, 4326),
    status driver_status_enum DEFAULT 'offline',
    rating DECIMAL(3,2) DEFAULT 0.0,
    total_trips INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ride requests table
CREATE TABLE public.ride_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    driver_id UUID REFERENCES public.drivers(id) ON DELETE SET NULL,
    pickup_location GEOGRAPHY(POINT, 4326) NOT NULL,
    pickup_address TEXT NOT NULL,
    drop_location GEOGRAPHY(POINT, 4326) NOT NULL,
    drop_address TEXT NOT NULL,
    estimated_fare DECIMAL(10,2) NOT NULL,
    actual_fare DECIMAL(10,2),
    distance_km DECIMAL(8,2),
    estimated_duration_minutes INTEGER,
    status ride_status_enum DEFAULT 'pending',
    payment_mode payment_mode_enum,
    special_instructions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ride history table (for completed rides with ratings)
CREATE TABLE public.ride_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    ride_id UUID REFERENCES public.ride_requests(id) ON DELETE CASCADE NOT NULL,
    customer_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    driver_id UUID REFERENCES public.drivers(id) ON DELETE CASCADE NOT NULL,
    customer_rating INTEGER CHECK (customer_rating >= 1 AND customer_rating <= 5),
    driver_rating INTEGER CHECK (driver_rating >= 1 AND driver_rating <= 5),
    customer_feedback TEXT,
    driver_feedback TEXT,
    payment_mode payment_mode_enum NOT NULL,
    final_fare DECIMAL(10,2) NOT NULL,
    tip_amount DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Driver locations table (for real-time tracking)
CREATE TABLE public.driver_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES public.drivers(id) ON DELETE CASCADE NOT NULL,
    location GEOGRAPHY(POINT, 4326) NOT NULL,
    heading DECIMAL(5,2), -- Direction in degrees
    speed_kmh DECIMAL(5,2), -- Speed in km/h
    accuracy_meters DECIMAL(8,2),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL, -- 'ride_request', 'ride_update', 'payment', etc.
    data JSONB, -- Additional data for the notification
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_phone ON public.users(phone);
CREATE INDEX idx_users_user_type ON public.users(user_type);
CREATE INDEX idx_drivers_status ON public.drivers(status);
CREATE INDEX idx_drivers_location ON public.drivers USING GIST(location);
CREATE INDEX idx_ride_requests_customer_id ON public.ride_requests(customer_id);
CREATE INDEX idx_ride_requests_driver_id ON public.ride_requests(driver_id);
CREATE INDEX idx_ride_requests_status ON public.ride_requests(status);
CREATE INDEX idx_ride_requests_created_at ON public.ride_requests(created_at);
CREATE INDEX idx_ride_requests_pickup_location ON public.ride_requests USING GIST(pickup_location);
CREATE INDEX idx_ride_requests_drop_location ON public.ride_requests USING GIST(drop_location);
CREATE INDEX idx_ride_history_customer_id ON public.ride_history(customer_id);
CREATE INDEX idx_ride_history_driver_id ON public.ride_history(driver_id);
CREATE INDEX idx_driver_locations_driver_id ON public.driver_locations(driver_id);
CREATE INDEX idx_driver_locations_timestamp ON public.driver_locations(timestamp);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_is_read ON public.notifications(is_read);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_drivers_updated_at BEFORE UPDATE ON public.drivers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ride_requests_updated_at BEFORE UPDATE ON public.ride_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
