{"logs": [{"outputFile": "com.quickride.customer.app-mergeDebugResources-79:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7b90347aefdb8277a15a649c7d5249c4\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "48,49,69,70,71,78,79,194,195,196,197,199,200,202,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4414,4508,6867,6966,7067,7698,7775,18649,18740,18822,18888,19035,19116,19279,19452,19529,19601", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "4503,4588,6961,7062,7151,7770,7863,18735,18817,18883,18951,19111,19193,19346,19524,19596,19718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a80351878203576e14d29104a2365afc\\transformed\\play-services-basement-18.1.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5586", "endColumns": "149", "endOffsets": "5731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\83e5e2b178069d0cc8cbffee7b842817\\transformed\\navigation-ui-2.7.6\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,122", "endOffsets": "156,279"}, "to": {"startLines": "192,193", "startColumns": "4,4", "startOffsets": "18420,18526", "endColumns": "105,122", "endOffsets": "18521,18644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4a7df0f92ff33183244383dbcbaff3f8\\transformed\\foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "19723,19813", "endColumns": "89,89", "endOffsets": "19808,19898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1347aa300b36a7cab68a2bd9530e812a\\transformed\\core-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,203", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3392,3488,3590,3688,3793,3898,4010,19351", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3483,3585,3683,3788,3893,4005,4121,19447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f8d4be1a1c7480e23037799cb5e0b535\\transformed\\material3-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,393,507,604,705,823,960,1082,1234,1324,1420,1518,1620,1738,1861,1962,2094,2226,2355,2522,2644,2768,2895,3017,3116,3215,3336,3457,3560,3671,3779,3918,4062,4170,4276,4359,4457,4554,4638,4723,4823,4903,4988,5085,5188,5285,5390,5480,5588,5691,5801,5919,5999,6104", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "166,279,388,502,599,700,818,955,1077,1229,1319,1415,1513,1615,1733,1856,1957,2089,2221,2350,2517,2639,2763,2890,3012,3111,3210,3331,3452,3555,3666,3774,3913,4057,4165,4271,4354,4452,4549,4633,4718,4818,4898,4983,5080,5183,5280,5385,5475,5583,5686,5796,5914,5994,6099,6198"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7933,8049,8162,8271,8385,8482,8583,8701,8838,8960,9112,9202,9298,9396,9498,9616,9739,9840,9972,10104,10233,10400,10522,10646,10773,10895,10994,11093,11214,11335,11438,11549,11657,11796,11940,12048,12154,12237,12335,12432,12516,12601,12701,12781,12866,12963,13066,13163,13268,13358,13466,13569,13679,13797,13877,13982", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "8044,8157,8266,8380,8477,8578,8696,8833,8955,9107,9197,9293,9391,9493,9611,9734,9835,9967,10099,10228,10395,10517,10641,10768,10890,10989,11088,11209,11330,11433,11544,11652,11791,11935,12043,12149,12232,12330,12427,12511,12596,12696,12776,12861,12958,13061,13158,13263,13353,13461,13564,13674,13792,13872,13977,14076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7dbe9381c9d479d3e9bf94a1e5011545\\transformed\\play-services-wallet-18.1.3\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "75", "endOffsets": "277"}, "to": {"startLines": "209", "startColumns": "4", "startOffsets": "19903", "endColumns": "79", "endOffsets": "19978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\471cdef36052226776d107dcedf318a4\\transformed\\play-services-base-18.1.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4593,4704,4857,4988,5094,5237,5363,5479,5736,5877,5983,6132,6258,6406,6545,6611,6681", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "4699,4852,4983,5089,5232,5358,5474,5581,5872,5978,6127,6253,6401,6540,6606,6676,6759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3e85b7c3ad0df798520dc2031c12bb41\\transformed\\appcompat-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,19198", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,19274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a207269cd93e115977cde60dd2975130\\transformed\\material-1.11.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1025,1118,1193,1258,1346,1411,1477,1535,1606,1672,1726,1836,1896,1960,2014,2087,2203,2287,2368,2501,2586,2671,2804,2894,2968,3020,3071,3137,3214,3296,3380,3454,3528,3607,3684,3756,3863,3952,4028,4119,4214,4288,4361,4455,4509,4583,4655,4741,4827,4889,4953,5016,5087,5188,5291,5386,5486,5542,5597", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "263,339,413,496,585,667,763,871,955,1020,1113,1188,1253,1341,1406,1472,1530,1601,1667,1721,1831,1891,1955,2009,2082,2198,2282,2363,2496,2581,2666,2799,2889,2963,3015,3066,3132,3209,3291,3375,3449,3523,3602,3679,3751,3858,3947,4023,4114,4209,4283,4356,4450,4504,4578,4650,4736,4822,4884,4948,5011,5082,5183,5286,5381,5481,5537,5592,5671"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,72,73,77,80,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2988,3064,3138,3221,3310,4126,4222,4330,7156,7221,7623,7868,14081,14169,14234,14300,14358,14429,14495,14549,14659,14719,14783,14837,14910,15026,15110,15191,15324,15409,15494,15627,15717,15791,15843,15894,15960,16037,16119,16203,16277,16351,16430,16507,16579,16686,16775,16851,16942,17037,17111,17184,17278,17332,17406,17478,17564,17650,17712,17776,17839,17910,18011,18114,18209,18309,18365,18956", "endLines": "5,33,34,35,36,37,45,46,47,72,73,77,80,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,198", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "313,3059,3133,3216,3305,3387,4217,4325,4409,7216,7309,7693,7928,14164,14229,14295,14353,14424,14490,14544,14654,14714,14778,14832,14905,15021,15105,15186,15319,15404,15489,15622,15712,15786,15838,15889,15955,16032,16114,16198,16272,16346,16425,16502,16574,16681,16770,16846,16937,17032,17106,17179,17273,17327,17401,17473,17559,17645,17707,17771,17834,17905,18006,18109,18204,18304,18360,18415,19030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f7c442f9f9f43b1a617e6ca6fe146292\\transformed\\browser-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "68,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "6764,7314,7415,7524", "endColumns": "102,100,108,98", "endOffsets": "6862,7410,7519,7618"}}]}]}