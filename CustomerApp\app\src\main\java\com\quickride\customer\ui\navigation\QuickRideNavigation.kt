package com.quickride.customer.ui.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.quickride.customer.ui.screens.auth.LoginScreen
import com.quickride.customer.ui.screens.home.HomeScreen

@Composable
fun QuickRideNavigation() {
    val navController = rememberNavController()
    
    NavHost(
        navController = navController,
        startDestination = "login"
    ) {
        composable("login") {
            LoginScreen(
                onNavigateToHome = {
                    navController.navigate("home") {
                        popUpTo("login") { inclusive = true }
                    }
                },
                onNavigateToRegister = {
                    navController.navigate("register")
                }
            )
        }
        
        composable("register") {
            // TODO: Implement RegisterScreen
        }
        
        composable("home") {
            HomeScreen(
                onNavigateToProfile = {
                    navController.navigate("profile")
                },
                onNavigateToBooking = {
                    navController.navigate("booking")
                }
            )
        }
        
        composable("profile") {
            // TODO: Implement ProfileScreen
        }
        
        composable("booking") {
            // TODO: Implement BookingScreen
        }
    }
}
