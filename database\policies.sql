-- QuickRide Row Level Security Policies
-- Run this script after schema.sql and functions.sql

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ride_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ride_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.driver_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Anyone can insert user profile on signup" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Drivers table policies
CREATE POLICY "Drivers can view their own profile" ON public.drivers
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Drivers can update their own profile" ON public.drivers
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Drivers can insert their own profile" ON public.drivers
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Customers can view available drivers" ON public.drivers
    FOR SELECT USING (
        status = 'available' 
        AND is_verified = true
        AND EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'customer'
        )
    );

-- Ride requests policies
CREATE POLICY "Customers can view their own ride requests" ON public.ride_requests
    FOR SELECT USING (customer_id = auth.uid());

CREATE POLICY "Drivers can view their assigned ride requests" ON public.ride_requests
    FOR SELECT USING (driver_id = auth.uid());

CREATE POLICY "Drivers can view pending ride requests" ON public.ride_requests
    FOR SELECT USING (
        status = 'pending' 
        AND EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'driver'
        )
    );

CREATE POLICY "Customers can create ride requests" ON public.ride_requests
    FOR INSERT WITH CHECK (
        customer_id = auth.uid() 
        AND EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'customer'
        )
    );

CREATE POLICY "Drivers can update their assigned rides" ON public.ride_requests
    FOR UPDATE USING (
        driver_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'driver'
        )
    );

CREATE POLICY "Customers can cancel their pending rides" ON public.ride_requests
    FOR UPDATE USING (
        customer_id = auth.uid() 
        AND status IN ('pending', 'accepted')
        AND EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'customer'
        )
    );

-- Ride history policies
CREATE POLICY "Customers can view their ride history" ON public.ride_history
    FOR SELECT USING (customer_id = auth.uid());

CREATE POLICY "Drivers can view their ride history" ON public.ride_history
    FOR SELECT USING (driver_id = auth.uid());

CREATE POLICY "Customers can update their ratings and feedback" ON public.ride_history
    FOR UPDATE USING (
        customer_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'customer'
        )
    );

CREATE POLICY "Drivers can update their ratings and feedback" ON public.ride_history
    FOR UPDATE USING (
        driver_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'driver'
        )
    );

-- Driver locations policies
CREATE POLICY "Drivers can insert their own location" ON public.driver_locations
    FOR INSERT WITH CHECK (
        driver_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'driver'
        )
    );

CREATE POLICY "Drivers can view their own location history" ON public.driver_locations
    FOR SELECT USING (driver_id = auth.uid());

CREATE POLICY "Customers can view assigned driver location" ON public.driver_locations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.ride_requests r
            JOIN public.users u ON u.id = auth.uid()
            WHERE r.driver_id = driver_locations.driver_id
            AND r.customer_id = auth.uid()
            AND r.status IN ('accepted', 'ongoing')
            AND u.user_type = 'customer'
        )
    );

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "System can insert notifications for users" ON public.notifications
    FOR INSERT WITH CHECK (true); -- This will be restricted by application logic

-- Create a function to handle user registration
CREATE OR REPLACE FUNCTION handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
    -- This function will be called by a trigger when a new user signs up
    -- The actual user profile creation will be handled by the application
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Grant specific permissions for PostGIS
GRANT SELECT ON spatial_ref_sys TO anon, authenticated;
