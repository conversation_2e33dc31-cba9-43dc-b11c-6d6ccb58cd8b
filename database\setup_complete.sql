-- QuickRide Complete Database Setup
-- Copy and paste this entire script into your Supabase SQL Editor and run it

-- ============================================================================
-- STEP 1: ENABLE EXTENSIONS
-- ============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- ============================================================================
-- STEP 2: CREATE CUSTOM TYPES
-- ============================================================================

-- Create custom types
DO $$ BEGIN
    CREATE TYPE user_type_enum AS ENUM ('customer', 'driver');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE driver_status_enum AS ENUM ('available', 'busy', 'offline');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE ride_status_enum AS ENUM ('pending', 'accepted', 'ongoing', 'completed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE payment_mode_enum AS ENUM ('cash', 'upi', 'card', 'wallet');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ============================================================================
-- STEP 3: CREATE TABLES
-- ============================================================================

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    name TEXT NOT NULL,
    phone TEXT UNIQUE,
    email TEXT UNIQUE,
    user_type user_type_enum NOT NULL,
    profile_image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Drivers table (additional info for drivers)
CREATE TABLE IF NOT EXISTS public.drivers (
    id UUID REFERENCES public.users(id) ON DELETE CASCADE PRIMARY KEY,
    name TEXT NOT NULL,
    vehicle_no TEXT UNIQUE NOT NULL,
    vehicle_model TEXT,
    vehicle_color TEXT,
    license_number TEXT UNIQUE NOT NULL,
    location GEOGRAPHY(POINT, 4326),
    status driver_status_enum DEFAULT 'offline',
    rating DECIMAL(3,2) DEFAULT 0.0,
    total_trips INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    vehicle_photo_url TEXT,
    license_photo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ride requests table
CREATE TABLE IF NOT EXISTS public.ride_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    customer_name TEXT NOT NULL,
    customer_phone TEXT NOT NULL,
    driver_id UUID REFERENCES public.drivers(id) ON DELETE SET NULL,
    pickup_location GEOGRAPHY(POINT, 4326) NOT NULL,
    pickup_address TEXT NOT NULL,
    drop_location GEOGRAPHY(POINT, 4326) NOT NULL,
    drop_address TEXT NOT NULL,
    estimated_fare DECIMAL(10,2) NOT NULL,
    actual_fare DECIMAL(10,2),
    distance_km DECIMAL(8,2),
    estimated_duration_minutes INTEGER,
    status ride_status_enum DEFAULT 'pending',
    payment_mode payment_mode_enum,
    special_instructions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ride history table
CREATE TABLE IF NOT EXISTS public.ride_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    ride_id UUID REFERENCES public.ride_requests(id) ON DELETE CASCADE NOT NULL,
    customer_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    customer_name TEXT NOT NULL,
    driver_id UUID REFERENCES public.drivers(id) ON DELETE CASCADE NOT NULL,
    driver_name TEXT NOT NULL,
    pickup_address TEXT NOT NULL,
    drop_address TEXT NOT NULL,
    customer_rating INTEGER CHECK (customer_rating >= 1 AND customer_rating <= 5),
    driver_rating INTEGER CHECK (driver_rating >= 1 AND driver_rating <= 5),
    customer_feedback TEXT,
    driver_feedback TEXT,
    payment_mode payment_mode_enum NOT NULL,
    final_fare DECIMAL(10,2) NOT NULL,
    tip_amount DECIMAL(10,2) DEFAULT 0,
    distance_km DECIMAL(8,2),
    duration_minutes INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Driver locations table (for real-time tracking)
CREATE TABLE IF NOT EXISTS public.driver_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES public.drivers(id) ON DELETE CASCADE NOT NULL,
    location GEOGRAPHY(POINT, 4326) NOT NULL,
    heading DECIMAL(5,2), -- Direction in degrees (0-360)
    speed_kmh DECIMAL(5,2), -- Speed in km/h
    accuracy_meters DECIMAL(8,2), -- GPS accuracy in meters
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL, -- 'ride_request', 'ride_update', 'payment', etc.
    data JSONB, -- Additional data for the notification
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 4: CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_phone ON public.users(phone);
CREATE INDEX IF NOT EXISTS idx_users_user_type ON public.users(user_type);
CREATE INDEX IF NOT EXISTS idx_drivers_status ON public.drivers(status);
CREATE INDEX IF NOT EXISTS idx_drivers_location ON public.drivers USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_ride_requests_customer_id ON public.ride_requests(customer_id);
CREATE INDEX IF NOT EXISTS idx_ride_requests_driver_id ON public.ride_requests(driver_id);
CREATE INDEX IF NOT EXISTS idx_ride_requests_status ON public.ride_requests(status);
CREATE INDEX IF NOT EXISTS idx_ride_requests_pickup_location ON public.ride_requests USING GIST(pickup_location);
CREATE INDEX IF NOT EXISTS idx_ride_requests_drop_location ON public.ride_requests USING GIST(drop_location);
CREATE INDEX IF NOT EXISTS idx_ride_requests_created_at ON public.ride_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_ride_history_customer_id ON public.ride_history(customer_id);
CREATE INDEX IF NOT EXISTS idx_ride_history_driver_id ON public.ride_history(driver_id);
CREATE INDEX IF NOT EXISTS idx_ride_history_created_at ON public.ride_history(created_at);
CREATE INDEX IF NOT EXISTS idx_driver_locations_driver_id ON public.driver_locations(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_locations_location ON public.driver_locations USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_driver_locations_timestamp ON public.driver_locations(timestamp);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(is_read);

-- ============================================================================
-- STEP 5: CREATE FUNCTIONS
-- ============================================================================

-- Function to calculate distance between two points using Haversine formula
CREATE OR REPLACE FUNCTION calculate_distance_km(
    lat1 DOUBLE PRECISION,
    lon1 DOUBLE PRECISION,
    lat2 DOUBLE PRECISION,
    lon2 DOUBLE PRECISION
) RETURNS DOUBLE PRECISION AS $$
DECLARE
    R CONSTANT DOUBLE PRECISION := 6371; -- Earth's radius in kilometers
    dLat DOUBLE PRECISION;
    dLon DOUBLE PRECISION;
    a DOUBLE PRECISION;
    c DOUBLE PRECISION;
BEGIN
    dLat := RADIANS(lat2 - lat1);
    dLon := RADIANS(lon2 - lon1);

    a := SIN(dLat/2) * SIN(dLat/2) +
         COS(RADIANS(lat1)) * COS(RADIANS(lat2)) *
         SIN(dLon/2) * SIN(dLon/2);
    c := 2 * ATAN2(SQRT(a), SQRT(1-a));

    RETURN R * c;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to calculate fare based on distance and time
CREATE OR REPLACE FUNCTION calculate_fare(
    distance_km DOUBLE PRECISION,
    duration_minutes INTEGER DEFAULT 0
) RETURNS DECIMAL(10,2) AS $$
DECLARE
    base_fare CONSTANT DECIMAL(10,2) := 50.00; -- Base fare in rupees
    per_km_rate CONSTANT DECIMAL(10,2) := 12.00; -- Rate per kilometer
    per_minute_rate CONSTANT DECIMAL(10,2) := 1.50; -- Rate per minute
    minimum_fare CONSTANT DECIMAL(10,2) := 80.00; -- Minimum fare
    calculated_fare DECIMAL(10,2);
BEGIN
    calculated_fare := base_fare + (distance_km * per_km_rate) + (duration_minutes * per_minute_rate);

    -- Ensure minimum fare
    IF calculated_fare < minimum_fare THEN
        calculated_fare := minimum_fare;
    END IF;

    RETURN calculated_fare;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to find nearest available drivers
CREATE OR REPLACE FUNCTION find_nearest_drivers(
    pickup_lat DOUBLE PRECISION,
    pickup_lon DOUBLE PRECISION,
    radius_km DOUBLE PRECISION DEFAULT 5.0,
    max_drivers INTEGER DEFAULT 10
) RETURNS TABLE(
    driver_id UUID,
    driver_name TEXT,
    vehicle_no TEXT,
    vehicle_model TEXT,
    distance_km DOUBLE PRECISION,
    rating DECIMAL(3,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        d.id,
        d.name,
        d.vehicle_no,
        d.vehicle_model,
        calculate_distance_km(
            pickup_lat,
            pickup_lon,
            ST_Y(d.location::geometry),
            ST_X(d.location::geometry)
        ) as distance_km,
        d.rating
    FROM public.drivers d
    WHERE d.status = 'available'
        AND d.is_verified = true
        AND d.location IS NOT NULL
        AND calculate_distance_km(
            pickup_lat,
            pickup_lon,
            ST_Y(d.location::geometry),
            ST_X(d.location::geometry)
        ) <= radius_km
    ORDER BY distance_km ASC
    LIMIT max_drivers;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- SUCCESS MESSAGE
-- ============================================================================

-- Show success message
DO $$
BEGIN
    RAISE NOTICE 'QuickRide database setup completed successfully!';
    RAISE NOTICE 'Schema, functions, and indexes have been created.';
    RAISE NOTICE 'Next: Run the policies and realtime configuration scripts.';
END $$;
