package com.quickride.driver.data.repository

import com.quickride.driver.data.model.RideRequest
import com.quickride.driver.data.model.DriverLocation
import com.quickride.driver.utils.Constants
import io.github.jan.supabase.realtime.PostgresAction
import io.github.jan.supabase.realtime.channel
import io.github.jan.supabase.realtime.postgresChangeFlow
import io.github.jan.supabase.realtime.realtime
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class RealtimeRepository(private val supabaseRepository: SupabaseRepository) {
    
    private val realtime = supabaseRepository.client.realtime
    
    /**
     * Subscribe to new ride requests in driver's area
     */
    fun subscribeToRideRequests(driverId: String, radiusKm: Double): Flow<RideRequest?> {
        val channel = realtime.channel("ride_requests_$driverId")
        
        return channel.postgresChangeFlow<RideRequest>(
            schema = "public",
            table = Constants.TABLE_RIDE_REQUESTS,
            filter = "status=eq.PENDING"
        ).map { change ->
            when (change.eventType) {
                PostgresAction.INSERT -> {
                    val rideRequest = change.record
                    // Additional filtering can be done here based on driver location
                    rideRequest
                }
                PostgresAction.UPDATE -> {
                    val rideRequest = change.record
                    if (rideRequest.status.name == "PENDING") rideRequest else null
                }
                else -> null
            }
        }
    }
    
    /**
     * Subscribe to ride updates for accepted rides
     */
    fun subscribeToAcceptedRides(driverId: String): Flow<RideRequest?> {
        val channel = realtime.channel("accepted_rides_$driverId")
        
        return channel.postgresChangeFlow<RideRequest>(
            schema = "public",
            table = Constants.TABLE_RIDE_REQUESTS,
            filter = "driver_id=eq.$driverId"
        ).map { change ->
            when (change.eventType) {
                PostgresAction.INSERT, PostgresAction.UPDATE -> change.record
                PostgresAction.DELETE -> null
                else -> null
            }
        }
    }
    
    /**
     * Subscribe to notifications for driver
     */
    fun subscribeToNotifications(driverId: String): Flow<String?> {
        val channel = realtime.channel("notifications_$driverId")
        
        return channel.postgresChangeFlow<Map<String, Any>>(
            schema = "public",
            table = Constants.TABLE_NOTIFICATIONS,
            filter = "user_id=eq.$driverId"
        ).map { change ->
            when (change.eventType) {
                PostgresAction.INSERT -> {
                    val notification = change.record
                    notification["message"] as? String
                }
                else -> null
            }
        }
    }
    
    /**
     * Subscribe to ride cancellations
     */
    fun subscribeToRideCancellations(driverId: String): Flow<String?> {
        val channel = realtime.channel("ride_cancellations_$driverId")
        
        return channel.postgresChangeFlow<RideRequest>(
            schema = "public",
            table = Constants.TABLE_RIDE_REQUESTS,
            filter = "driver_id=eq.$driverId"
        ).map { change ->
            when (change.eventType) {
                PostgresAction.UPDATE -> {
                    val rideRequest = change.record
                    if (rideRequest.status.name == "CANCELLED") {
                        rideRequest.id
                    } else null
                }
                PostgresAction.DELETE -> change.record.id
                else -> null
            }
        }
    }
    
    /**
     * Subscribe to customer location updates during active ride
     */
    fun subscribeToCustomerLocation(customerId: String): Flow<Map<String, Double>?> {
        val channel = realtime.channel("customer_location_$customerId")
        
        return channel.postgresChangeFlow<Map<String, Any>>(
            schema = "public",
            table = "user_locations", // Assuming we have a user_locations table
            filter = "user_id=eq.$customerId"
        ).map { change ->
            when (change.eventType) {
                PostgresAction.INSERT, PostgresAction.UPDATE -> {
                    val location = change.record
                    mapOf(
                        "latitude" to (location["latitude"] as? Double ?: 0.0),
                        "longitude" to (location["longitude"] as? Double ?: 0.0)
                    )
                }
                else -> null
            }
        }
    }
    
    /**
     * Publish driver location update
     */
    suspend fun publishDriverLocation(driverLocation: DriverLocation) {
        val channel = realtime.channel("driver_location_updates")
        
        // Insert/Update driver location in database
        // This will trigger realtime updates for customers
        supabaseRepository.database
            .from(Constants.TABLE_DRIVER_LOCATIONS)
            .upsert(driverLocation)
    }
    
    /**
     * Publish driver status update
     */
    suspend fun publishDriverStatus(driverId: String, status: String) {
        val channel = realtime.channel("driver_status_updates")
        
        // Update driver status in database
        supabaseRepository.database
            .from(Constants.TABLE_DRIVERS)
            .update(mapOf("status" to status, "updated_at" to "now()"))
            .eq("id", driverId)
    }
    
    /**
     * Start listening to all subscriptions
     */
    suspend fun connect() {
        realtime.connect()
    }
    
    /**
     * Stop all subscriptions
     */
    suspend fun disconnect() {
        realtime.disconnect()
    }
    
    /**
     * Join a specific channel for ride communication
     */
    suspend fun joinRideChannel(rideId: String) {
        val channel = realtime.channel("ride_$rideId")
        channel.join()
    }
    
    /**
     * Leave a specific ride channel
     */
    suspend fun leaveRideChannel(rideId: String) {
        val channel = realtime.channel("ride_$rideId")
        channel.leave()
    }
}
