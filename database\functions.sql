-- QuickRide Database Functions
-- Run this script after schema.sql

-- Function to calculate distance between two points using Haversine formula
CREATE OR REPLACE FUNCTION calculate_distance_km(
    lat1 DECIMAL, lon1 DECIMAL, 
    lat2 DECIMAL, lon2 DECIMAL
) RETURNS DECIMAL AS $$
DECLARE
    R DECIMAL := 6371; -- Earth's radius in kilometers
    dLat DECIMAL;
    dLon DECIMAL;
    a DECIMAL;
    c DECIMAL;
BEGIN
    dLat := RADIANS(lat2 - lat1);
    dLon := RADIANS(lon2 - lon1);
    
    a := SIN(dLat/2) * SIN(dLat/2) + 
         COS(RADIANS(lat1)) * COS(RADIANS(lat2)) * 
         SIN(dLon/2) * SIN(dLon/2);
    
    c := 2 * ATAN2(SQRT(a), SQRT(1-a));
    
    RETURN R * c;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate fare based on distance
CREATE OR REPLACE FUNCTION calculate_fare(distance_km DECIMAL) 
R<PERSON><PERSON>NS DECIMAL AS $$
DECLARE
    base_fare DECIMAL := 50.0; -- Base fare in rupees
    per_km_rate DECIMAL := 12.0; -- Rate per km
    minimum_fare DECIMAL := 80.0; -- Minimum fare
    calculated_fare DECIMAL;
BEGIN
    calculated_fare := base_fare + (distance_km * per_km_rate);
    
    -- Ensure minimum fare
    IF calculated_fare < minimum_fare THEN
        calculated_fare := minimum_fare;
    END IF;
    
    -- Round to nearest 5 rupees
    calculated_fare := ROUND(calculated_fare / 5) * 5;
    
    RETURN calculated_fare;
END;
$$ LANGUAGE plpgsql;

-- Function to find nearest available drivers
CREATE OR REPLACE FUNCTION find_nearest_drivers(
    pickup_lat DECIMAL, 
    pickup_lon DECIMAL, 
    search_radius_km DECIMAL DEFAULT 10,
    max_drivers INTEGER DEFAULT 5
) RETURNS TABLE (
    driver_id UUID,
    driver_name TEXT,
    vehicle_no TEXT,
    vehicle_model TEXT,
    distance_km DECIMAL,
    rating DECIMAL,
    location_lat DECIMAL,
    location_lon DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id,
        d.name,
        d.vehicle_no,
        d.vehicle_model,
        calculate_distance_km(
            pickup_lat, pickup_lon,
            ST_Y(d.location::geometry), ST_X(d.location::geometry)
        ) as distance_km,
        d.rating,
        ST_Y(d.location::geometry) as location_lat,
        ST_X(d.location::geometry) as location_lon
    FROM public.drivers d
    WHERE 
        d.status = 'available'
        AND d.is_verified = true
        AND d.location IS NOT NULL
        AND calculate_distance_km(
            pickup_lat, pickup_lon,
            ST_Y(d.location::geometry), ST_X(d.location::geometry)
        ) <= search_radius_km
    ORDER BY distance_km ASC
    LIMIT max_drivers;
END;
$$ LANGUAGE plpgsql;

-- Function to create a new ride request with fare calculation
CREATE OR REPLACE FUNCTION create_ride_request(
    p_customer_id UUID,
    p_pickup_lat DECIMAL,
    p_pickup_lon DECIMAL,
    p_pickup_address TEXT,
    p_drop_lat DECIMAL,
    p_drop_lon DECIMAL,
    p_drop_address TEXT,
    p_special_instructions TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    ride_id UUID;
    distance DECIMAL;
    fare DECIMAL;
    duration INTEGER;
BEGIN
    -- Calculate distance
    distance := calculate_distance_km(p_pickup_lat, p_pickup_lon, p_drop_lat, p_drop_lon);
    
    -- Calculate fare
    fare := calculate_fare(distance);
    
    -- Estimate duration (assuming average speed of 30 km/h in city)
    duration := ROUND(distance * 2); -- 2 minutes per km
    
    -- Insert ride request
    INSERT INTO public.ride_requests (
        customer_id,
        pickup_location,
        pickup_address,
        drop_location,
        drop_address,
        estimated_fare,
        distance_km,
        estimated_duration_minutes,
        special_instructions
    ) VALUES (
        p_customer_id,
        ST_SetSRID(ST_MakePoint(p_pickup_lon, p_pickup_lat), 4326),
        p_pickup_address,
        ST_SetSRID(ST_MakePoint(p_drop_lon, p_drop_lat), 4326),
        p_drop_address,
        fare,
        distance,
        duration,
        p_special_instructions
    ) RETURNING id INTO ride_id;
    
    RETURN ride_id;
END;
$$ LANGUAGE plpgsql;

-- Function to accept a ride request
CREATE OR REPLACE FUNCTION accept_ride_request(
    p_ride_id UUID,
    p_driver_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    ride_status TEXT;
BEGIN
    -- Check if ride is still pending
    SELECT status INTO ride_status 
    FROM public.ride_requests 
    WHERE id = p_ride_id;
    
    IF ride_status != 'pending' THEN
        RETURN FALSE; -- Ride already accepted or cancelled
    END IF;
    
    -- Update ride request
    UPDATE public.ride_requests 
    SET 
        driver_id = p_driver_id,
        status = 'accepted',
        accepted_at = NOW()
    WHERE id = p_ride_id AND status = 'pending';
    
    -- Update driver status
    UPDATE public.drivers 
    SET status = 'busy' 
    WHERE id = p_driver_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to update driver location
CREATE OR REPLACE FUNCTION update_driver_location(
    p_driver_id UUID,
    p_lat DECIMAL,
    p_lon DECIMAL,
    p_heading DECIMAL DEFAULT NULL,
    p_speed_kmh DECIMAL DEFAULT NULL,
    p_accuracy_meters DECIMAL DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    -- Update driver's current location
    UPDATE public.drivers 
    SET 
        location = ST_SetSRID(ST_MakePoint(p_lon, p_lat), 4326),
        updated_at = NOW()
    WHERE id = p_driver_id;
    
    -- Insert location history
    INSERT INTO public.driver_locations (
        driver_id,
        location,
        heading,
        speed_kmh,
        accuracy_meters
    ) VALUES (
        p_driver_id,
        ST_SetSRID(ST_MakePoint(p_lon, p_lat), 4326),
        p_heading,
        p_speed_kmh,
        p_accuracy_meters
    );
    
    -- Clean up old location history (keep only last 24 hours)
    DELETE FROM public.driver_locations 
    WHERE driver_id = p_driver_id 
    AND timestamp < NOW() - INTERVAL '24 hours';
END;
$$ LANGUAGE plpgsql;

-- Function to complete a ride
CREATE OR REPLACE FUNCTION complete_ride(
    p_ride_id UUID,
    p_actual_fare DECIMAL,
    p_payment_mode payment_mode_enum
) RETURNS VOID AS $$
DECLARE
    ride_record RECORD;
BEGIN
    -- Get ride details
    SELECT * INTO ride_record 
    FROM public.ride_requests 
    WHERE id = p_ride_id;
    
    -- Update ride request
    UPDATE public.ride_requests 
    SET 
        status = 'completed',
        actual_fare = p_actual_fare,
        payment_mode = p_payment_mode,
        completed_at = NOW()
    WHERE id = p_ride_id;
    
    -- Create ride history entry
    INSERT INTO public.ride_history (
        ride_id,
        customer_id,
        driver_id,
        payment_mode,
        final_fare
    ) VALUES (
        p_ride_id,
        ride_record.customer_id,
        ride_record.driver_id,
        p_payment_mode,
        p_actual_fare
    );
    
    -- Update driver status and stats
    UPDATE public.drivers 
    SET 
        status = 'available',
        total_trips = total_trips + 1
    WHERE id = ride_record.driver_id;
END;
$$ LANGUAGE plpgsql;
