Capp/src/main/java/com/quickride/customer/viewmodel/AuthViewModel.kt;app/src/main/java/com/quickride/customer/utils/Constants.ktMapp/src/main/java/com/quickride/customer/ui/navigation/QuickRideNavigation.ktGapp/src/main/java/com/quickride/customer/ui/screens/auth/LoginScreen.ktJapp/src/main/java/com/quickride/customer/data/repository/AuthRepository.kt9app/src/main/java/com/quickride/customer/ui/theme/Type.ktNapp/src/main/java/com/quickride/customer/data/repository/RealtimeRepository.kt8app/src/main/java/com/quickride/customer/MainActivity.ktFapp/src/main/java/com/quickride/customer/ui/screens/home/<USER>/src/main/java/com/quickride/customer/ui/theme/Color.ktNapp/src/main/java/com/quickride/customer/data/repository/SupabaseRepository.kt@app/src/main/java/com/quickride/customer/QuickRideApplication.kt:app/src/main/java/com/quickride/customer/ui/theme/Theme.kt;app/src/main/java/com/quickride/customer/data/model/User.kt;app/src/main/java/com/quickride/customer/data/model/Ride.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        