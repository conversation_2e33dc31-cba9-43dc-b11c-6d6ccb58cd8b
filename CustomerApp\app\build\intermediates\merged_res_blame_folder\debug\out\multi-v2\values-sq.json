{"logs": [{"outputFile": "com.quickride.customer.app-mergeDebugResources-79:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c43d48e9c6fdd0b13b309be79ed23630\\transformed\\play-services-wallet-18.1.3\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "68", "endOffsets": "270"}, "to": {"startLines": "206", "startColumns": "4", "startOffsets": "19839", "endColumns": "72", "endOffsets": "19907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8df16a82633c27d58115a5978e1a627a\\transformed\\browser-1.7.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "73,112,113,114", "startColumns": "4,4,4,4", "startOffsets": "7546,11854,11955,12066", "endColumns": "114,100,110,100", "endOffsets": "7656,11950,12061,12162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4290410644c2485a14201712d52f6902\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,382,484,580,661,754,846,936,1005,1072,1159,1250,1323,1400,1466", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "195,278,377,479,575,656,749,841,931,1000,1067,1154,1245,1318,1395,1461,1582"}, "to": {"startLines": "52,53,105,106,108,116,117,176,177,178,179,181,182,185,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4951,5046,11242,11341,11516,12237,12318,17150,17242,17332,17401,17551,17638,17893,18247,18324,18390", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "5041,5124,11336,11438,11607,12313,12406,17237,17327,17396,17463,17633,17724,17961,18319,18385,18506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1d4f6475d09c648c826009f0591a7b9b\\transformed\\navigation-ui-2.7.6\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,118", "endOffsets": "156,275"}, "to": {"startLines": "174,175", "startColumns": "4,4", "startOffsets": "16925,17031", "endColumns": "105,118", "endOffsets": "17026,17145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9744ab552975b8309e8f515f2f57272b\\transformed\\material3-1.1.2\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,282,397,511,587,678,787,921,1033,1175,1255,1351,1439,1534,1648,1768,1869,2002,2132,2272,2457,2591,2710,2831,2954,3043,3135,3256,3393,3484,3587,3692,3826,3967,4074,4168,4241,4318,4400,4479,4580,4656,4735,4830,4927,5018,5112,5196,5301,5397,5495,5619,5695,5805", "endColumns": "114,111,114,113,75,90,108,133,111,141,79,95,87,94,113,119,100,132,129,139,184,133,118,120,122,88,91,120,136,90,102,104,133,140,106,93,72,76,81,78,100,75,78,94,96,90,93,83,104,95,97,123,75,109,102", "endOffsets": "165,277,392,506,582,673,782,916,1028,1170,1250,1346,1434,1529,1643,1763,1864,1997,2127,2267,2452,2586,2705,2826,2949,3038,3130,3251,3388,3479,3582,3687,3821,3962,4069,4163,4236,4313,4395,4474,4575,4651,4730,4825,4922,5013,5107,5191,5296,5392,5490,5614,5690,5800,5903"}, "to": {"startLines": "33,34,35,36,54,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,107,110,183,186,188,192,193,194,195,196,197,198,199,200,201,202,203,204,205", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3013,3128,3240,3355,5129,7661,7752,7861,7995,8107,8249,8329,8425,8513,8608,8722,8842,8943,9076,9206,9346,9531,9665,9784,9905,10028,10117,10209,10330,10467,10558,10661,10766,10900,11041,11148,11443,11677,17729,17966,18146,18511,18587,18666,18761,18858,18949,19043,19127,19232,19328,19426,19550,19626,19736", "endColumns": "114,111,114,113,75,90,108,133,111,141,79,95,87,94,113,119,100,132,129,139,184,133,118,120,122,88,91,120,136,90,102,104,133,140,106,93,72,76,81,78,100,75,78,94,96,90,93,83,104,95,97,123,75,109,102", "endOffsets": "3123,3235,3350,3464,5200,7747,7856,7990,8102,8244,8324,8420,8508,8603,8717,8837,8938,9071,9201,9341,9526,9660,9779,9900,10023,10112,10204,10325,10462,10553,10656,10761,10895,11036,11143,11237,11511,11749,17806,18040,18242,18582,18661,18756,18853,18944,19038,19122,19227,19323,19421,19545,19621,19731,19834"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b218b548b10776bee60a62556292e105\\transformed\\material-1.11.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,610,702,803,929,1012,1077,1177,1247,1306,1404,1466,1530,1589,1661,1724,1778,1895,1952,2014,2068,2140,2275,2358,2436,2577,2661,2743,2891,2981,3059,3112,3171,3237,3308,3387,3475,3558,3634,3712,3784,3857,3961,4050,4122,4216,4315,4389,4461,4562,4612,4697,4763,4853,4942,5004,5068,5131,5198,5314,5427,5536,5641,5698,5761", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,77,85,99,91,100,125,82,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,147,89,77,52,58,65,70,78,87,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82", "endOffsets": "262,341,419,505,605,697,798,924,1007,1072,1172,1242,1301,1399,1461,1525,1584,1656,1719,1773,1890,1947,2009,2063,2135,2270,2353,2431,2572,2656,2738,2886,2976,3054,3107,3166,3232,3303,3382,3470,3553,3629,3707,3779,3852,3956,4045,4117,4211,4310,4384,4456,4557,4607,4692,4758,4848,4937,4999,5063,5126,5193,5309,5422,5531,5636,5693,5756,5839"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,109,111,115,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3469,3548,3626,3712,3812,4641,4742,4868,11612,11754,12167,12411,12470,12568,12630,12694,12753,12825,12888,12942,13059,13116,13178,13232,13304,13439,13522,13600,13741,13825,13907,14055,14145,14223,14276,14335,14401,14472,14551,14639,14722,14798,14876,14948,15021,15125,15214,15286,15380,15479,15553,15625,15726,15776,15861,15927,16017,16106,16168,16232,16295,16362,16478,16591,16700,16805,16862,17468", "endLines": "5,37,38,39,40,41,49,50,51,109,111,115,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,180", "endColumns": "12,78,77,85,99,91,100,125,82,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,147,89,77,52,58,65,70,78,87,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82", "endOffsets": "312,3543,3621,3707,3807,3899,4737,4863,4946,11672,11849,12232,12465,12563,12625,12689,12748,12820,12883,12937,13054,13111,13173,13227,13299,13434,13517,13595,13736,13820,13902,14050,14140,14218,14271,14330,14396,14467,14546,14634,14717,14793,14871,14943,15016,15120,15209,15281,15375,15474,15548,15620,15721,15771,15856,15922,16012,16101,16163,16227,16290,16357,16473,16586,16695,16800,16857,16920,17546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f2cee423d1c2e3384a741a9ee7a53bff\\transformed\\play-services-basement-18.1.0\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6266", "endColumns": "128", "endOffsets": "6390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\502ac482ec8584223e67ff5e0bfb49fb\\transformed\\appcompat-1.6.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "317,431,531,643,729,835,958,1040,1118,1209,1302,1397,1491,1592,1685,1780,1877,1968,2061,2142,2248,2352,2450,2556,2660,2762,2916,17811", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "426,526,638,724,830,953,1035,1113,1204,1297,1392,1486,1587,1680,1775,1872,1963,2056,2137,2243,2347,2445,2551,2655,2757,2911,3008,17888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\98a043caa217b0d931e9538ad1a4e084\\transformed\\core-1.12.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "42,43,44,45,46,47,48,187", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3904,4003,4105,4203,4300,4408,4519,18045", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3998,4100,4198,4295,4403,4514,4636,18141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e793494f9a580077c629faff77594186\\transformed\\play-services-base-18.1.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5205,5312,5485,5622,5729,5890,6024,6150,6395,6565,6673,6848,6986,7148,7332,7397,7464", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "5307,5480,5617,5724,5885,6019,6145,6261,6560,6668,6843,6981,7143,7327,7392,7459,7541"}}]}]}