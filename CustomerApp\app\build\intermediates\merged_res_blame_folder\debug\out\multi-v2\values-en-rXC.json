{"logs": [{"outputFile": "com.quickride.customer.app-mergeDebugResources-79:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4290410644c2485a14201712d52f6902\\transformed\\ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,1985,2152,2333,2518,2684,2863,3030", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1980,2147,2328,2513,2679,2858,3025,3263"}, "to": {"startLines": "40,41,75,76,78,83,84,85,86,87,88,89,90,93,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7807,7998,15153,15350,15726,16704,16889,17082,17270,17457,17622,17789,17970,18527,19283,19462,19629", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "7993,8178,15345,15547,15908,16884,17077,17265,17452,17617,17784,17965,18150,18688,19457,19624,19862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\98a043caa217b0d931e9538ad1a4e084\\transformed\\core-1.12.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "33,34,35,36,37,38,39,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6380,6576,6781,6982,7183,7390,7595,18878", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "6571,6776,6977,7178,7385,7590,7802,19077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\502ac482ec8584223e67ff5e0bfb49fb\\transformed\\appcompat-1.6.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,18341", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,18522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9744ab552975b8309e8f515f2f57272b\\transformed\\material3-1.1.2\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,272,485,695,907,1088,1281,1493,1728,1943,2178,2361,2559,2748,2943,3161,3385,3588,3814,4035,4262,4518,4736,4951,5174,5398,5589,5786,6002,6223,6418,6619,6820,7041,7278,7483,7677,7851,8030,8216,8401,8602,8781,8964,9163,9363,9562,9760,9947,10152,10351,10550,10765,10942,11140", "endColumns": "216,212,209,211,180,192,211,234,214,234,182,197,188,194,217,223,202,225,220,226,255,217,214,222,223,190,196,215,220,194,200,200,220,236,204,193,173,178,185,184,200,178,182,198,199,198,197,186,204,198,198,214,176,197,193", "endOffsets": "267,480,690,902,1083,1276,1488,1723,1938,2173,2356,2554,2743,2938,3156,3380,3583,3809,4030,4257,4513,4731,4946,5169,5393,5584,5781,5997,6218,6413,6614,6815,7036,7273,7478,7672,7846,8025,8211,8396,8597,8776,8959,9158,9358,9557,9755,9942,10147,10346,10545,10760,10937,11135,11329"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,79,91,94,96,100,101,102,103,104,105,106,107,108,109,110,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5528,5745,5958,6168,8183,8564,8757,8969,9204,9419,9654,9837,10035,10224,10419,10637,10861,11064,11290,11511,11738,11994,12212,12427,12650,12874,13065,13262,13478,13699,13894,14095,14296,14517,14754,14959,15552,15913,18155,18693,19082,19867,20046,20229,20428,20628,20827,21025,21212,21417,21616,21815,22030,22207,22405", "endColumns": "216,212,209,211,180,192,211,234,214,234,182,197,188,194,217,223,202,225,220,226,255,217,214,222,223,190,196,215,220,194,200,200,220,236,204,193,173,178,185,184,200,178,182,198,199,198,197,186,204,198,198,214,176,197,193", "endOffsets": "5740,5953,6163,6375,8359,8752,8964,9199,9414,9649,9832,10030,10219,10414,10632,10856,11059,11285,11506,11733,11989,12207,12422,12645,12869,13060,13257,13473,13694,13889,14090,14291,14512,14749,14954,15148,15721,16087,18336,18873,19278,20041,20224,20423,20623,20822,21020,21207,21412,21611,21810,22025,22202,22400,22594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8df16a82633c27d58115a5978e1a627a\\transformed\\browser-1.7.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,255,454,665", "endColumns": "199,198,210,201", "endOffsets": "250,449,660,862"}, "to": {"startLines": "43,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "8364,16092,16291,16502", "endColumns": "199,198,210,201", "endOffsets": "8559,16286,16497,16699"}}]}]}