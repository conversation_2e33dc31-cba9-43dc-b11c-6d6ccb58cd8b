-- Enable realtime for tables
ALTER PUBLICATION supabase_realtime ADD TABLE users;
ALTER PUBLICATION supabase_realtime ADD TABLE drivers;
ALTER PUBLICATION supabase_realtime ADD TABLE ride_requests;
ALTER PUBLICATION supabase_realtime ADD TABLE ride_history;
ALTER PUBLICATION supabase_realtime ADD TABLE driver_locations;
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;

-- Create indexes for realtime performance
CREATE INDEX IF NOT EXISTS idx_ride_requests_status ON ride_requests(status);
CREATE INDEX IF NOT EXISTS idx_ride_requests_customer_id ON ride_requests(customer_id);
CREATE INDEX IF NOT EXISTS idx_ride_requests_driver_id ON ride_requests(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_locations_driver_id ON driver_locations(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_locations_timestamp ON driver_locations(timestamp);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

-- Create function to notify ride request updates
CREATE OR REPLACE FUNCTION notify_ride_request_update()
RETURNS TRIGGER AS $$
BEGIN
    -- Notify customers about ride status changes
    IF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        PERFORM pg_notify(
            'ride_status_update',
            json_build_object(
                'ride_id', NEW.id,
                'customer_id', NEW.customer_id,
                'driver_id', NEW.driver_id,
                'old_status', OLD.status,
                'new_status', NEW.status,
                'updated_at', NEW.updated_at
            )::text
        );
    END IF;
    
    -- Notify drivers about new ride requests
    IF TG_OP = 'INSERT' AND NEW.status = 'pending' THEN
        PERFORM pg_notify(
            'new_ride_request',
            json_build_object(
                'ride_id', NEW.id,
                'pickup_lat', ST_Y(NEW.pickup_location),
                'pickup_lng', ST_X(NEW.pickup_location),
                'drop_lat', ST_Y(NEW.drop_location),
                'drop_lng', ST_X(NEW.drop_location),
                'estimated_fare', NEW.estimated_fare,
                'created_at', NEW.created_at
            )::text
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for ride request notifications
DROP TRIGGER IF EXISTS trigger_ride_request_update ON ride_requests;
CREATE TRIGGER trigger_ride_request_update
    AFTER INSERT OR UPDATE ON ride_requests
    FOR EACH ROW
    EXECUTE FUNCTION notify_ride_request_update();

-- Create function to notify driver location updates
CREATE OR REPLACE FUNCTION notify_driver_location_update()
RETURNS TRIGGER AS $$
BEGIN
    -- Notify customers tracking their driver
    PERFORM pg_notify(
        'driver_location_update',
        json_build_object(
            'driver_id', NEW.driver_id,
            'latitude', ST_Y(NEW.location),
            'longitude', ST_X(NEW.location),
            'heading', NEW.heading,
            'speed_kmh', NEW.speed_kmh,
            'timestamp', NEW.timestamp
        )::text
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for driver location notifications
DROP TRIGGER IF EXISTS trigger_driver_location_update ON driver_locations;
CREATE TRIGGER trigger_driver_location_update
    AFTER INSERT OR UPDATE ON driver_locations
    FOR EACH ROW
    EXECUTE FUNCTION notify_driver_location_update();

-- Create function to notify driver status changes
CREATE OR REPLACE FUNCTION notify_driver_status_update()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        PERFORM pg_notify(
            'driver_status_update',
            json_build_object(
                'driver_id', NEW.id,
                'old_status', OLD.status,
                'new_status', NEW.status,
                'updated_at', NEW.updated_at
            )::text
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for driver status notifications
DROP TRIGGER IF EXISTS trigger_driver_status_update ON drivers;
CREATE TRIGGER trigger_driver_status_update
    AFTER UPDATE ON drivers
    FOR EACH ROW
    EXECUTE FUNCTION notify_driver_status_update();

-- Create function to clean up old location data
CREATE OR REPLACE FUNCTION cleanup_old_locations()
RETURNS void AS $$
BEGIN
    -- Delete location records older than 24 hours
    DELETE FROM driver_locations 
    WHERE timestamp < NOW() - INTERVAL '24 hours';
    
    -- Delete old notifications older than 7 days
    DELETE FROM notifications 
    WHERE created_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- Create scheduled job to clean up old data (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-old-locations', '0 2 * * *', 'SELECT cleanup_old_locations();');

-- Grant necessary permissions for realtime
GRANT USAGE ON SCHEMA realtime TO anon, authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA realtime TO anon, authenticated;

-- Enable realtime for specific operations
GRANT INSERT, UPDATE, DELETE ON ride_requests TO authenticated;
GRANT INSERT, UPDATE, DELETE ON driver_locations TO authenticated;
GRANT INSERT, UPDATE, DELETE ON notifications TO authenticated;

-- Create realtime publication for specific events
CREATE PUBLICATION ride_updates FOR TABLE ride_requests
WHERE (status IN ('pending', 'accepted', 'ongoing', 'completed', 'cancelled'));

CREATE PUBLICATION driver_location_updates FOR TABLE driver_locations;

CREATE PUBLICATION notification_updates FOR TABLE notifications;
