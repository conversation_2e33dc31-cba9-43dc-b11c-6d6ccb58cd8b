<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme (Dark Theme) -->
    <style name="Theme.QuickRideDriver" parent="Theme.Material3.DayNight">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/background</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/background</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        <!-- Action bar -->
        <item name="colorPrimaryDark">@color/primary_dark</item>
    </style>

    <!-- Theme without action bar -->
    <style name="Theme.QuickRideDriver.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Splash screen theme -->
    <style name="Theme.QuickRideDriver.Splash" parent="Theme.QuickRideDriver.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background_driver</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Button styles -->
    <style name="Button.Primary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/button_primary</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <style name="Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/button_secondary</item>
        <item name="android:textColor">@color/button_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <style name="Button.Danger" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/button_danger</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <style name="Button.Success" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/button_success</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <!-- Text input styles -->
    <style name="TextInputLayout.Primary" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
        <item name="android:textColorHint">@color/gray_400</item>
    </style>

    <!-- Card styles -->
    <style name="Card.Primary" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <style name="Card.Ride" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/surface_variant</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">8dp</item>
        <item name="android:layout_margin">12dp</item>
    </style>

    <!-- Text styles -->
    <style name="Text.Headline" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/on_background</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text.Title" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textColor">@color/on_background</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text.Body" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/on_background</item>
    </style>

    <style name="Text.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/on_surface_variant</item>
    </style>

    <style name="Text.Earnings" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textColor">@color/earnings_positive</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- Bottom navigation style -->
    <style name="BottomNavigation.Primary" parent="Widget.Material3.BottomNavigationView">
        <item name="itemIconTint">@color/primary</item>
        <item name="itemTextColor">@color/primary</item>
        <item name="android:background">@color/surface</item>
        <item name="elevation">8dp</item>
    </style>

    <!-- Toolbar style -->
    <style name="Toolbar.Primary" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/surface</item>
        <item name="titleTextColor">@color/on_surface</item>
        <item name="subtitleTextColor">@color/on_surface_variant</item>
    </style>

    <!-- Status indicator styles -->
    <style name="StatusIndicator.Available">
        <item name="android:background">@color/status_available</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">8dp</item>
    </style>

    <style name="StatusIndicator.Busy">
        <item name="android:background">@color/status_busy</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">8dp</item>
    </style>

    <style name="StatusIndicator.Offline">
        <item name="android:background">@color/status_offline</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">8dp</item>
    </style>
</resources>
