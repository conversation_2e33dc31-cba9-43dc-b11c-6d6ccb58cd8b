<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Internet permission for API calls -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- Location permissions -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    
    <!-- Phone permission for authentication -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    
    <!-- Notification permissions -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    
    <!-- Camera permission for profile pictures -->
    <uses-permission android:name="android.permission.CAMERA" />
    
    <!-- Storage permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application
        android:name=".QuickRideApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.QuickRideCustomer"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- Google Maps API Key -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="${MAPS_API_KEY}" />

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.QuickRideCustomer">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Authentication Activity -->
        <activity
            android:name=".ui.auth.AuthActivity"
            android:exported="false"
            android:theme="@style/Theme.QuickRideCustomer.NoActionBar" />

        <!-- Splash Activity -->
        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.QuickRideCustomer.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Ride Activity -->
        <activity
            android:name=".ui.ride.RideActivity"
            android:exported="false"
            android:theme="@style/Theme.QuickRideCustomer.NoActionBar" />

        <!-- Profile Activity -->
        <activity
            android:name=".ui.profile.ProfileActivity"
            android:exported="false" />

        <!-- History Activity -->
        <activity
            android:name=".ui.history.HistoryActivity"
            android:exported="false" />

        <!-- Payment Activity -->
        <activity
            android:name=".ui.payment.PaymentActivity"
            android:exported="false" />

        <!-- Location Service -->
        <service
            android:name=".service.LocationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" />

        <!-- Firebase Messaging Service -->
        <service
            android:name=".service.NotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Razorpay Payment Activity -->
        <activity
            android:name="com.razorpay.CheckoutActivity"
            android:exported="false"
            android:theme="@style/CheckoutTheme"
            tools:replace="android:exported" />

    </application>

</manifest>
