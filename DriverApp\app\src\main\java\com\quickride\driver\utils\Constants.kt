package com.quickride.driver.utils

object Constants {
    // Supabase Configuration
    const val SUPABASE_URL = "https://rrvqtlnzzwmyowbeehqt.supabase.co"
    const val SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJydnF0bG56endteW93YmVlaHF0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NDMyMTksImV4cCI6MjA2NjUxOTIxOX0.GqjhlBzRiY-nvG9H2nnH4XuXzZtsIEOMV2lcIxhdBbU"
    
    // Database Tables
    const val TABLE_USERS = "users"
    const val TABLE_DRIVERS = "drivers"
    const val TABLE_RIDE_REQUESTS = "ride_requests"
    const val TABLE_RIDE_HISTORY = "ride_history"
    const val TABLE_DRIVER_LOCATIONS = "driver_locations"
    const val TABLE_NOTIFICATIONS = "notifications"
    
    // Realtime Channels
    const val CHANNEL_RIDE_REQUESTS = "ride_requests"
    const val CHANNEL_DRIVER_LOCATIONS = "driver_locations"
    const val CHANNEL_NOTIFICATIONS = "notifications"
    
    // Shared Preferences
    const val PREF_NAME = "quickride_driver_prefs"
    const val PREF_DRIVER_ID = "driver_id"
    const val PREF_DRIVER_NAME = "driver_name"
    const val PREF_DRIVER_PHONE = "driver_phone"
    const val PREF_VEHICLE_NO = "vehicle_no"
    const val PREF_IS_LOGGED_IN = "is_logged_in"
    const val PREF_IS_ONLINE = "is_online"
    const val PREF_LAST_LOCATION_LAT = "last_location_lat"
    const val PREF_LAST_LOCATION_LNG = "last_location_lng"
    
    // Location Tracking
    const val LOCATION_UPDATE_INTERVAL = 3000L // 3 seconds for drivers
    const val LOCATION_FASTEST_INTERVAL = 1000L // 1 second
    const val DEFAULT_ZOOM = 16f
    const val LOCATION_TRACKING_NOTIFICATION_ID = 1001
    
    // Driver Status
    const val STATUS_AVAILABLE = "AVAILABLE"
    const val STATUS_BUSY = "BUSY"
    const val STATUS_OFFLINE = "OFFLINE"
    
    // Ride Management
    const val RIDE_ACCEPT_TIMEOUT_SECONDS = 15
    const val RIDE_ARRIVAL_RADIUS_METERS = 100.0
    const val CUSTOMER_WAIT_TIME_MINUTES = 5
    
    // Earnings
    const val COMMISSION_PERCENTAGE = 20.0 // 20% commission
    
    // Notification
    const val NOTIFICATION_CHANNEL_ID = "quickride_driver_notifications"
    const val NOTIFICATION_CHANNEL_NAME = "QuickRide Driver Notifications"
    const val RIDE_REQUEST_NOTIFICATION_ID = 2001
    const val LOCATION_SERVICE_NOTIFICATION_ID = 2002
    
    // Request Codes
    const val REQUEST_CODE_LOCATION_PERMISSION = 1001
    const val REQUEST_CODE_PHONE_PERMISSION = 1002
    const val REQUEST_CODE_CAMERA_PERMISSION = 1003
    const val REQUEST_CODE_STORAGE_PERMISSION = 1004
    const val REQUEST_CODE_BACKGROUND_LOCATION = 1005
    
    // Intent Extras
    const val EXTRA_RIDE_ID = "ride_id"
    const val EXTRA_CUSTOMER_ID = "customer_id"
    const val EXTRA_PICKUP_LAT = "pickup_lat"
    const val EXTRA_PICKUP_LNG = "pickup_lng"
    const val EXTRA_DROP_LAT = "drop_lat"
    const val EXTRA_DROP_LNG = "drop_lng"
    const val EXTRA_ESTIMATED_FARE = "estimated_fare"
    
    // Service Actions
    const val ACTION_START_LOCATION_TRACKING = "start_location_tracking"
    const val ACTION_STOP_LOCATION_TRACKING = "stop_location_tracking"
    const val ACTION_UPDATE_DRIVER_STATUS = "update_driver_status"
    
    // Vehicle Types
    const val VEHICLE_TYPE_AUTO = "AUTO"
    const val VEHICLE_TYPE_BIKE = "BIKE"
    const val VEHICLE_TYPE_CAR = "CAR"
    
    // Document Types
    const val DOC_TYPE_LICENSE = "LICENSE"
    const val DOC_TYPE_VEHICLE_PHOTO = "VEHICLE_PHOTO"
    const val DOC_TYPE_PROFILE_PHOTO = "PROFILE_PHOTO"
}
