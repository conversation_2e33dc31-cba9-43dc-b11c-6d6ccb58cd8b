package com.quickride.customer.data.model

import kotlinx.serialization.Serializable

@Serializable
data class User(
    val id: String,
    val name: String,
    val phone: String,
    val userType: String,
    val profileImageUrl: String? = null,
    val isActive: Boolean = true,
    val createdAt: String,
    val updatedAt: String
)

@Serializable
data class Driver(
    val id: String,
    val name: String,
    val vehicleNo: String,
    val vehicleModel: String? = null,
    val vehicleColor: String? = null,
    val licenseNumber: String,
    val location: Location? = null,
    val status: DriverStatus = DriverStatus.OFFLINE,
    val rating: Double = 0.0,
    val totalTrips: Int = 0,
    val isVerified: Boolean = false,
    val createdAt: String,
    val updatedAt: String
)

@Serializable
data class Location(
    val latitude: Double,
    val longitude: Double
)

enum class DriverStatus {
    AVAILABLE, BUSY, OFFLINE
}

enum class UserType {
    CUSTOMER, DRIVER
}
