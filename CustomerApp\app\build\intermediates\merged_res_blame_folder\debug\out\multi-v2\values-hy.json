{"logs": [{"outputFile": "com.quickride.customer.app-mergeDebugResources-79:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\98a043caa217b0d931e9538ad1a4e084\\transformed\\core-1.12.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "42,43,44,45,46,47,48,187", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3882,3982,4087,4185,4284,4389,4491,17984", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3977,4082,4180,4279,4384,4486,4597,18080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4290410644c2485a14201712d52f6902\\transformed\\ui-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,380,480,563,645,731,826,908,980,1051,1136,1224,1298,1379,1448", "endColumns": "98,81,93,99,82,81,85,94,81,71,70,84,87,73,80,68,117", "endOffsets": "199,281,375,475,558,640,726,821,903,975,1046,1131,1219,1293,1374,1443,1561"}, "to": {"startLines": "52,53,105,106,108,116,117,176,177,178,179,181,182,185,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4908,5007,11200,11294,11481,12205,12287,17084,17179,17261,17333,17489,17574,17829,18195,18276,18345", "endColumns": "98,81,93,99,82,81,85,94,81,71,70,84,87,73,80,68,117", "endOffsets": "5002,5084,11289,11389,11559,12282,12368,17174,17256,17328,17399,17569,17657,17898,18271,18340,18458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c43d48e9c6fdd0b13b309be79ed23630\\transformed\\play-services-wallet-18.1.3\\res\\values-hy\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "76", "endOffsets": "278"}, "to": {"startLines": "206", "startColumns": "4", "startOffsets": "19740", "endColumns": "80", "endOffsets": "19816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9744ab552975b8309e8f515f2f57272b\\transformed\\material3-1.1.2\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,278,389,501,578,676,792,934,1053,1205,1288,1398,1489,1584,1702,1820,1923,2059,2193,2322,2495,2620,2732,2848,2968,3060,3154,3273,3410,3512,3613,3717,3851,3991,4096,4193,4280,4358,4442,4523,4633,4709,4788,4883,4980,5067,5159,5241,5341,5435,5530,5643,5719,5820", "endColumns": "111,110,110,111,76,97,115,141,118,151,82,109,90,94,117,117,102,135,133,128,172,124,111,115,119,91,93,118,136,101,100,103,133,139,104,96,86,77,83,80,109,75,78,94,96,86,91,81,99,93,94,112,75,100,89", "endOffsets": "162,273,384,496,573,671,787,929,1048,1200,1283,1393,1484,1579,1697,1815,1918,2054,2188,2317,2490,2615,2727,2843,2963,3055,3149,3268,3405,3507,3608,3712,3846,3986,4091,4188,4275,4353,4437,4518,4628,4704,4783,4878,4975,5062,5154,5236,5336,5430,5525,5638,5714,5815,5905"}, "to": {"startLines": "33,34,35,36,54,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,107,110,183,186,188,192,193,194,195,196,197,198,199,200,201,202,203,204,205", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3024,3136,3247,3358,5089,7585,7683,7799,7941,8060,8212,8295,8405,8496,8591,8709,8827,8930,9066,9200,9329,9502,9627,9739,9855,9975,10067,10161,10280,10417,10519,10620,10724,10858,10998,11103,11394,11628,17662,17903,18085,18463,18539,18618,18713,18810,18897,18989,19071,19171,19265,19360,19473,19549,19650", "endColumns": "111,110,110,111,76,97,115,141,118,151,82,109,90,94,117,117,102,135,133,128,172,124,111,115,119,91,93,118,136,101,100,103,133,139,104,96,86,77,83,80,109,75,78,94,96,86,91,81,99,93,94,112,75,100,89", "endOffsets": "3131,3242,3353,3465,5161,7678,7794,7936,8055,8207,8290,8400,8491,8586,8704,8822,8925,9061,9195,9324,9497,9622,9734,9850,9970,10062,10156,10275,10412,10514,10615,10719,10853,10993,11098,11195,11476,11701,17741,17979,18190,18534,18613,18708,18805,18892,18984,19066,19166,19260,19355,19468,19544,19645,19735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b218b548b10776bee60a62556292e105\\transformed\\material-1.11.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1047,1144,1229,1291,1378,1440,1504,1565,1632,1693,1747,1869,1926,1986,2040,2121,2256,2340,2425,2561,2636,2711,2854,2949,3029,3085,3138,3204,3278,3357,3443,3526,3597,3673,3749,3826,3932,4020,4100,4196,4292,4366,4444,4544,4595,4679,4748,4835,4926,4988,5052,5115,5186,5291,5397,5497,5600,5660,5717", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,142,94,79,55,52,65,73,78,85,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84", "endOffsets": "260,336,412,492,584,672,767,897,978,1042,1139,1224,1286,1373,1435,1499,1560,1627,1688,1742,1864,1921,1981,2035,2116,2251,2335,2420,2556,2631,2706,2849,2944,3024,3080,3133,3199,3273,3352,3438,3521,3592,3668,3744,3821,3927,4015,4095,4191,4287,4361,4439,4539,4590,4674,4743,4830,4921,4983,5047,5110,5181,5286,5392,5492,5595,5655,5712,5797"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,109,111,115,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3470,3546,3622,3702,3794,4602,4697,4827,11564,11706,12120,12373,12435,12522,12584,12648,12709,12776,12837,12891,13013,13070,13130,13184,13265,13400,13484,13569,13705,13780,13855,13998,14093,14173,14229,14282,14348,14422,14501,14587,14670,14741,14817,14893,14970,15076,15164,15244,15340,15436,15510,15588,15688,15739,15823,15892,15979,16070,16132,16196,16259,16330,16435,16541,16641,16744,16804,17404", "endLines": "5,37,38,39,40,41,49,50,51,109,111,115,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,180", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,142,94,79,55,52,65,73,78,85,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84", "endOffsets": "310,3541,3617,3697,3789,3877,4692,4822,4903,11623,11798,12200,12430,12517,12579,12643,12704,12771,12832,12886,13008,13065,13125,13179,13260,13395,13479,13564,13700,13775,13850,13993,14088,14168,14224,14277,14343,14417,14496,14582,14665,14736,14812,14888,14965,15071,15159,15239,15335,15431,15505,15583,15683,15734,15818,15887,15974,16065,16127,16191,16254,16325,16430,16536,16636,16739,16799,16856,17484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e793494f9a580077c629faff77594186\\transformed\\play-services-base-18.1.0\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,586,690,840,972,1095,1204,1367,1471,1635,1767,1925,2087,2148,2211", "endColumns": "101,160,129,103,149,131,122,108,162,103,163,131,157,161,60,62,77", "endOffsets": "294,455,585,689,839,971,1094,1203,1366,1470,1634,1766,1924,2086,2147,2210,2288"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5166,5272,5437,5571,5679,5833,5969,6096,6360,6527,6635,6803,6939,7101,7267,7332,7399", "endColumns": "105,164,133,107,153,135,126,112,166,107,167,135,161,165,64,66,81", "endOffsets": "5267,5432,5566,5674,5828,5964,6091,6204,6522,6630,6798,6934,7096,7262,7327,7394,7476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8df16a82633c27d58115a5978e1a627a\\transformed\\browser-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "73,112,113,114", "startColumns": "4,4,4,4", "startOffsets": "7481,11803,11906,12017", "endColumns": "103,102,110,102", "endOffsets": "7580,11901,12012,12115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f2cee423d1c2e3384a741a9ee7a53bff\\transformed\\play-services-basement-18.1.0\\res\\values-hy\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6209", "endColumns": "150", "endOffsets": "6355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1d4f6475d09c648c826009f0591a7b9b\\transformed\\navigation-ui-2.7.6\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,116", "endOffsets": "156,273"}, "to": {"startLines": "174,175", "startColumns": "4,4", "startOffsets": "16861,16967", "endColumns": "105,116", "endOffsets": "16962,17079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\502ac482ec8584223e67ff5e0bfb49fb\\transformed\\appcompat-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,17746", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,17824"}}]}]}