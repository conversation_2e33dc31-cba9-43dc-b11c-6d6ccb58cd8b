package com.quickride.customer.data.repository

import com.quickride.customer.data.model.RideRequest
import com.quickride.customer.data.model.NearbyDriver
import com.quickride.customer.utils.Constants
import io.github.jan.supabase.realtime.PostgresAction
import io.github.jan.supabase.realtime.channel
import io.github.jan.supabase.realtime.postgresChangeFlow
import io.github.jan.supabase.realtime.realtime
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.json.Json

class RealtimeRepository(private val supabaseRepository: SupabaseRepository) {
    
    private val realtime = supabaseRepository.client.realtime
    
    /**
     * Subscribe to ride request updates for a specific customer
     */
    fun subscribeToRideUpdates(customerId: String): Flow<RideRequest?> {
        val channel = realtime.channel("ride_updates_$customerId")
        
        return channel.postgresChangeFlow<RideRequest>(
            schema = "public",
            table = Constants.TABLE_RIDE_REQUESTS,
            filter = "customer_id=eq.$customerId"
        ).map { change ->
            when (change.eventType) {
                PostgresAction.INSERT, PostgresAction.UPDATE -> change.record
                PostgresAction.DELETE -> null
                else -> null
            }
        }
    }
    
    /**
     * Subscribe to driver location updates for nearby drivers
     */
    fun subscribeToDriverLocations(bounds: Map<String, Double>): Flow<List<NearbyDriver>> {
        val channel = realtime.channel("driver_locations")
        
        return channel.postgresChangeFlow<NearbyDriver>(
            schema = "public",
            table = Constants.TABLE_DRIVER_LOCATIONS
        ).map { change ->
            // Filter drivers within bounds and available status
            when (change.eventType) {
                PostgresAction.INSERT, PostgresAction.UPDATE -> {
                    val driver = change.record
                    if (isDriverInBounds(driver, bounds) && driver.status == "AVAILABLE") {
                        listOf(driver)
                    } else {
                        emptyList()
                    }
                }
                else -> emptyList()
            }
        }
    }
    
    /**
     * Subscribe to notifications for a specific customer
     */
    fun subscribeToNotifications(customerId: String): Flow<String?> {
        val channel = realtime.channel("notifications_$customerId")
        
        return channel.postgresChangeFlow<Map<String, Any>>(
            schema = "public",
            table = Constants.TABLE_NOTIFICATIONS,
            filter = "user_id=eq.$customerId"
        ).map { change ->
            when (change.eventType) {
                PostgresAction.INSERT -> {
                    val notification = change.record
                    notification["message"] as? String
                }
                else -> null
            }
        }
    }
    
    /**
     * Subscribe to ride status changes for active ride
     */
    fun subscribeToActiveRide(rideId: String): Flow<RideRequest?> {
        val channel = realtime.channel("active_ride_$rideId")
        
        return channel.postgresChangeFlow<RideRequest>(
            schema = "public",
            table = Constants.TABLE_RIDE_REQUESTS,
            filter = "id=eq.$rideId"
        ).map { change ->
            when (change.eventType) {
                PostgresAction.UPDATE -> change.record
                PostgresAction.DELETE -> null
                else -> change.record
            }
        }
    }
    
    /**
     * Subscribe to driver location for active ride
     */
    fun subscribeToDriverLocation(driverId: String): Flow<NearbyDriver?> {
        val channel = realtime.channel("driver_location_$driverId")
        
        return channel.postgresChangeFlow<NearbyDriver>(
            schema = "public",
            table = Constants.TABLE_DRIVER_LOCATIONS,
            filter = "driver_id=eq.$driverId"
        ).map { change ->
            when (change.eventType) {
                PostgresAction.INSERT, PostgresAction.UPDATE -> change.record
                else -> null
            }
        }
    }
    
    /**
     * Start listening to all subscriptions
     */
    suspend fun connect() {
        realtime.connect()
    }
    
    /**
     * Stop all subscriptions
     */
    suspend fun disconnect() {
        realtime.disconnect()
    }
    
    /**
     * Check if driver is within specified bounds
     */
    private fun isDriverInBounds(driver: NearbyDriver, bounds: Map<String, Double>): Boolean {
        val lat = driver.location.latitude
        val lng = driver.location.longitude
        
        val northEast = bounds["northEast"] ?: return false
        val southWest = bounds["southWest"] ?: return false
        val eastBound = bounds["east"] ?: return false
        val westBound = bounds["west"] ?: return false
        
        return lat <= northEast && lat >= southWest && 
               lng <= eastBound && lng >= westBound
    }
}
