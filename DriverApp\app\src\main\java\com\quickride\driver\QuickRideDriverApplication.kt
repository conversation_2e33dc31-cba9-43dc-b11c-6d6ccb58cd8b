package com.quickride.driver

import android.app.Application
import com.quickride.driver.data.repository.SupabaseRepository

class QuickRideDriverApplication : Application() {
    
    // Repository instance
    val supabaseRepository by lazy { SupabaseRepository() }
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        
        // Initialize Supabase
        supabaseRepository.initialize()
    }
    
    companion object {
        lateinit var instance: QuickRideDriverApplication
            private set
    }
}
