package com.quickride.customer.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// QuickRide Brand Colors
val Primary = Color(0xFF2196F3)
val PrimaryVariant = Color(0xFF1976D2)
val Secondary = Color(0xFF009688)
val SecondaryVariant = Color(0xFF00695C)

// Status Colors
val Success = Color(0xFF4CAF50)
val Warning = Color(0xFFFF9800)
val Error = Color(0xFFF44336)
val Info = Color(0xFF2196F3)

// Ride Status Colors
val RidePending = Color(0xFFFF9800)
val RideAccepted = Color(0xFF2196F3)
val RideOngoing = Color(0xFF4CAF50)
val RideCompleted = Color(0xFF9E9E9E)
val RideCancelled = Color(0xFFF44336)

// Map Colors
val PickupMarker = Color(0xFF4CAF50)
val DropMarker = Color(0xFFF44336)
val DriverMarker = Color(0xFF2196F3)
val RouteColor = Color(0xFF2196F3)
