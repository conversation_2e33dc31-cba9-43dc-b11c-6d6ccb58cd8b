<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.quickride.customer"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <!-- Internet permission for API calls -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Location permissions -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- Phone permission for authentication -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />

    <!-- Notification permissions -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- Camera permission for profile pictures -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- Storage permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data
                android:mimeType="*/*"
                android:scheme="*" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data
                android:host="pay"
                android:mimeType="*/*"
                android:scheme="upi" />
        </intent>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
        <intent>
            <action android:name="android.intent.action.SEND" />

            <data android:mimeType="*/*" />
        </intent>
        <intent>
            <action android:name="rzp.device_token.share" />
        </intent>
        <!-- Needs to be explicitly declared on Android R+ -->
        <package android:name="com.google.android.apps.maps" />
    </queries>

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <permission
        android:name="com.quickride.customer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.quickride.customer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.quickride.customer.QuickRideApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.QuickRideCustomer"
        android:usesCleartextTraffic="true" >

        <!-- Google Maps API Key -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyDummy_Key_Replace_With_Your_Actual_Key" />

        <!-- Main Activity -->
        <activity
            android:name="com.quickride.customer.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.QuickRideCustomer" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Authentication Activity -->
        <activity
            android:name="com.quickride.customer.ui.auth.AuthActivity"
            android:exported="false"
            android:theme="@style/Theme.QuickRideCustomer.NoActionBar" />

        <!-- Splash Activity -->
        <activity
            android:name="com.quickride.customer.ui.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.QuickRideCustomer.Splash" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Ride Activity -->
        <activity
            android:name="com.quickride.customer.ui.ride.RideActivity"
            android:exported="false"
            android:theme="@style/Theme.QuickRideCustomer.NoActionBar" />

        <!-- Profile Activity -->
        <activity
            android:name="com.quickride.customer.ui.profile.ProfileActivity"
            android:exported="false" />

        <!-- History Activity -->
        <activity
            android:name="com.quickride.customer.ui.history.HistoryActivity"
            android:exported="false" />

        <!-- Payment Activity -->
        <activity
            android:name="com.quickride.customer.ui.payment.PaymentActivity"
            android:exported="false" />

        <!-- Location Service -->
        <service
            android:name="com.quickride.customer.service.LocationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" />

        <!-- Firebase Messaging Service -->
        <service
            android:name="com.quickride.customer.service.NotificationService"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Razorpay Payment Activity -->
        <activity
            android:name="com.razorpay.CheckoutActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:theme="@style/CheckoutTheme" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
        </activity>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.quickride.customer.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="io.github.jan.supabase.gotrue.SupabaseInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="com.russhwolf.settings.SettingsInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="com.razorpay.RzpTokenReceiver"
            android:exported="true" >
            <intent-filter>
                <action android:name="rzp.device_token.share" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="com.razorpay.plugin.googlepay_all"
            android:value="com.razorpay.RzpGpayMerged" /> <!-- Needs to be explicitly declared on P+ -->
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <!--
            Service handling Google Sign-In user revocation. For apps that do not integrate with
            Google Sign-In, this service will never be started.
        -->
        <service
            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
            android:exported="true"
            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />
        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>