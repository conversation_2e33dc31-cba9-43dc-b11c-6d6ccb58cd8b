package com.quickride.customer.data.repository

import com.quickride.customer.data.model.User
import com.quickride.customer.utils.Constants
import io.github.jan.supabase.gotrue.auth
import io.github.jan.supabase.gotrue.providers.builtin.Email
import io.github.jan.supabase.gotrue.providers.builtin.Phone
import io.github.jan.supabase.gotrue.user.UserInfo
import io.github.jan.supabase.postgrest.from
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class AuthRepository(private val supabaseRepository: SupabaseRepository) {
    
    private val auth = supabaseRepository.auth
    private val database = supabaseRepository.database
    
    /**
     * Sign up with email and password
     */
    suspend fun signUpWithEmail(email: String, password: String, name: String): Result<User> {
        return try {
            val result = auth.signUpWith(Email) {
                this.email = email
                this.password = password
                data = buildJsonObject {
                    put("name", name)
                    put("user_type", "CUSTOMER")
                }
            }
            
            result.user?.let { userInfo ->
                val user = createUserProfile(userInfo, name, email, null)
                Result.success(user)
            } ?: Result.failure(Exception("Failed to create user"))
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Sign up with phone number
     */
    suspend fun signUpWithPhone(phone: String, name: String): Result<String> {
        return try {
            auth.signUpWith(Phone) {
                phoneNumber = phone
                data = buildJsonObject {
                    put("name", name)
                    put("user_type", "CUSTOMER")
                }
            }
            Result.success("OTP sent to $phone")
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Verify phone OTP
     */
    suspend fun verifyPhoneOTP(phone: String, otp: String, name: String): Result<User> {
        return try {
            val result = auth.verifyPhoneOtp(
                type = io.github.jan.supabase.gotrue.providers.builtin.OtpType.SMS,
                phoneNumber = phone,
                token = otp
            )
            
            result.user?.let { userInfo ->
                val user = createUserProfile(userInfo, name, null, phone)
                Result.success(user)
            } ?: Result.failure(Exception("Failed to verify OTP"))
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Sign in with email and password
     */
    suspend fun signInWithEmail(email: String, password: String): Result<User> {
        return try {
            val result = auth.signInWith(Email) {
                this.email = email
                this.password = password
            }
            
            result.user?.let { userInfo ->
                val user = getUserProfile(userInfo.id)
                Result.success(user)
            } ?: Result.failure(Exception("Failed to sign in"))
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Sign in with phone number
     */
    suspend fun signInWithPhone(phone: String): Result<String> {
        return try {
            auth.signInWith(Phone) {
                phoneNumber = phone
            }
            Result.success("OTP sent to $phone")
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Verify phone OTP for sign in
     */
    suspend fun verifySignInOTP(phone: String, otp: String): Result<User> {
        return try {
            val result = auth.verifyPhoneOtp(
                type = io.github.jan.supabase.gotrue.providers.builtin.OtpType.SMS,
                phoneNumber = phone,
                token = otp
            )
            
            result.user?.let { userInfo ->
                val user = getUserProfile(userInfo.id)
                Result.success(user)
            } ?: Result.failure(Exception("Failed to verify OTP"))
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Sign out current user
     */
    suspend fun signOut(): Result<Unit> {
        return try {
            auth.signOut()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get current user session
     */
    suspend fun getCurrentUser(): User? {
        return try {
            val session = auth.currentSessionOrNull()
            session?.user?.let { userInfo ->
                getUserProfile(userInfo.id)
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Check if user is signed in
     */
    fun isSignedIn(): Boolean {
        return auth.currentSessionOrNull() != null
    }
    
    /**
     * Listen to auth state changes
     */
    fun authStateFlow(): Flow<User?> = flow {
        auth.sessionStatus.collect { status ->
            when (status) {
                is io.github.jan.supabase.gotrue.SessionStatus.Authenticated -> {
                    val user = getUserProfile(status.session.user.id)
                    emit(user)
                }
                is io.github.jan.supabase.gotrue.SessionStatus.NotAuthenticated -> {
                    emit(null)
                }
                else -> {
                    // Loading state
                }
            }
        }
    }
    
    /**
     * Update user profile
     */
    suspend fun updateProfile(userId: String, updates: Map<String, Any>): Result<User> {
        return try {
            database.from(Constants.TABLE_USERS)
                .update(updates + mapOf("updated_at" to "now()"))
                .eq("id", userId)
            
            val user = getUserProfile(userId)
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Reset password
     */
    suspend fun resetPassword(email: String): Result<String> {
        return try {
            auth.resetPasswordForEmail(email)
            Result.success("Password reset email sent")
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Create user profile in database
     */
    private suspend fun createUserProfile(
        userInfo: UserInfo,
        name: String,
        email: String?,
        phone: String?
    ): User {
        val user = User(
            id = userInfo.id,
            name = name,
            email = email,
            phone = phone,
            userType = com.quickride.customer.data.model.UserType.CUSTOMER,
            isVerified = userInfo.emailConfirmedAt != null || userInfo.phoneConfirmedAt != null,
            createdAt = userInfo.createdAt.toString(),
            updatedAt = userInfo.updatedAt.toString()
        )
        
        // Insert user profile into database
        database.from(Constants.TABLE_USERS).insert(user)
        
        return user
    }
    
    /**
     * Get user profile from database
     */
    private suspend fun getUserProfile(userId: String): User {
        val result = database.from(Constants.TABLE_USERS)
            .select()
            .eq("id", userId)
            .decodeSingle<User>()
        
        return result
    }
}
