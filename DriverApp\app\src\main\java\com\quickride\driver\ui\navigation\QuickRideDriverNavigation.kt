package com.quickride.driver.ui.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController

@Composable
fun QuickRideDriverNavigation(
    navController: NavHostController = rememberNavController()
) {
    NavHost(
        navController = navController,
        startDestination = "splash"
    ) {
        composable("splash") {
            // SplashScreen()
        }
        
        composable("auth") {
            // AuthScreen()
        }
        
        composable("vehicle_registration") {
            // VehicleRegistrationScreen()
        }
        
        composable("home") {
            // HomeScreen()
        }
        
        composable("ride/{rideId}") { backStackEntry ->
            val rideId = backStackEntry.arguments?.getString("rideId") ?: ""
            // RideScreen(rideId = rideId)
        }
        
        composable("earnings") {
            // EarningsScreen()
        }
        
        composable("profile") {
            // ProfileScreen()
        }
        
        composable("history") {
            // HistoryScreen()
        }
    }
}
