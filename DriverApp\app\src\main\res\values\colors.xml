<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Primary Colors (Dark Theme) -->
    <color name="primary">#FFC107</color>
    <color name="primary_variant">#FF8F00</color>
    <color name="primary_light">#FFF9C4</color>
    <color name="primary_dark">#FF6F00</color>
    
    <!-- Secondary Colors -->
    <color name="secondary">#607D8B</color>
    <color name="secondary_variant">#455A64</color>
    <color name="secondary_light">#CFD8DC</color>
    
    <!-- Background Colors (Dark Theme) -->
    <color name="background">#121212</color>
    <color name="surface">#1E1E1E</color>
    <color name="surface_variant">#2C2C2C</color>
    
    <!-- Text Colors (Dark Theme) -->
    <color name="on_primary">#000000</color>
    <color name="on_secondary">#FFFFFF</color>
    <color name="on_background">#FFFFFF</color>
    <color name="on_surface">#FFFFFF</color>
    <color name="on_surface_variant">#BDBDBD</color>
    
    <!-- Status Colors -->
    <color name="success">#4CAF50</color>
    <color name="warning">#FF9800</color>
    <color name="error">#F44336</color>
    <color name="info">#2196F3</color>
    
    <!-- Driver Status Colors -->
    <color name="status_available">#4CAF50</color>
    <color name="status_busy">#FF9800</color>
    <color name="status_offline">#9E9E9E</color>
    
    <!-- Ride Status Colors -->
    <color name="ride_pending">#FF9800</color>
    <color name="ride_accepted">#2196F3</color>
    <color name="ride_ongoing">#4CAF50</color>
    <color name="ride_completed">#9E9E9E</color>
    <color name="ride_cancelled">#F44336</color>
    
    <!-- Map Colors -->
    <color name="pickup_marker">#4CAF50</color>
    <color name="drop_marker">#F44336</color>
    <color name="driver_marker">#FFC107</color>
    <color name="route_color">#FFC107</color>
    
    <!-- Card Colors (Dark Theme) -->
    <color name="card_background">#1E1E1E</color>
    <color name="card_elevation">#424242</color>
    
    <!-- Button Colors -->
    <color name="button_primary">#FFC107</color>
    <color name="button_secondary">#607D8B</color>
    <color name="button_danger">#F44336</color>
    <color name="button_success">#4CAF50</color>
    <color name="button_disabled">#616161</color>
    
    <!-- Earnings Colors -->
    <color name="earnings_positive">#4CAF50</color>
    <color name="earnings_neutral">#FFC107</color>
    <color name="earnings_background">#2C2C2C</color>
    
    <!-- Rating Colors -->
    <color name="rating_star">#FFC107</color>
    <color name="rating_star_empty">#616161</color>
    
    <!-- Notification Colors -->
    <color name="notification_background">#323232</color>
    <color name="notification_accent">#FFC107</color>
    
    <!-- Transparent Colors -->
    <color name="transparent">#00000000</color>
    <color name="semi_transparent_black">#80000000</color>
    <color name="semi_transparent_white">#80FFFFFF</color>
    <color name="semi_transparent_yellow">#80FFC107</color>
    
    <!-- Material Design Colors -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="gray_50">#FAFAFA</color>
    <color name="gray_100">#F5F5F5</color>
    <color name="gray_200">#EEEEEE</color>
    <color name="gray_300">#E0E0E0</color>
    <color name="gray_400">#BDBDBD</color>
    <color name="gray_500">#9E9E9E</color>
    <color name="gray_600">#757575</color>
    <color name="gray_700">#616161</color>
    <color name="gray_800">#424242</color>
    <color name="gray_900">#212121</color>
</resources>
