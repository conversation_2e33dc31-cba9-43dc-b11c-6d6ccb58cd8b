package com.quickride.customer

import androidx.multidex.MultiDexApplication
import com.quickride.customer.data.repository.SupabaseRepository

class QuickRideApplication : MultiDexApplication() {
    
    // Repository instance
    val supabaseRepository by lazy { SupabaseRepository() }
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        
        // Initialize Supabase
        supabaseRepository.initialize()
    }
    
    companion object {
        lateinit var instance: QuickRideApplication
            private set
    }
}
