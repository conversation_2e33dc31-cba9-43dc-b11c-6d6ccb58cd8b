package com.quickride.customer

import android.app.Application
import com.quickride.customer.data.repository.SupabaseRepository

class QuickRideApplication : Application() {
    
    // Repository instance
    val supabaseRepository by lazy { SupabaseRepository() }
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        
        // Initialize Supabase
        supabaseRepository.initialize()
    }
    
    companion object {
        lateinit var instance: QuickRideApplication
            private set
    }
}
