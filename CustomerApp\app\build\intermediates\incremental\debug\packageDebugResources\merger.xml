<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res"><file name="ic_car" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\drawable\ic_car.xml" qualifiers="" type="drawable"/><file name="ic_location" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\drawable\ic_location.xml" qualifiers="" type="drawable"/><file name="splash_background" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary">#2196F3</color><color name="primary_variant">#1976D2</color><color name="primary_light">#BBDEFB</color><color name="primary_dark">#0D47A1</color><color name="secondary">#009688</color><color name="secondary_variant">#00695C</color><color name="secondary_light">#B2DFDB</color><color name="background">#FFFFFF</color><color name="surface">#FFFFFF</color><color name="surface_variant">#F5F5F5</color><color name="on_primary">#FFFFFF</color><color name="on_secondary">#FFFFFF</color><color name="on_background">#212121</color><color name="on_surface">#212121</color><color name="on_surface_variant">#757575</color><color name="success">#4CAF50</color><color name="warning">#FF9800</color><color name="error">#F44336</color><color name="info">#2196F3</color><color name="ride_pending">#FF9800</color><color name="ride_accepted">#2196F3</color><color name="ride_ongoing">#4CAF50</color><color name="ride_completed">#9E9E9E</color><color name="ride_cancelled">#F44336</color><color name="pickup_marker">#4CAF50</color><color name="drop_marker">#F44336</color><color name="driver_marker">#2196F3</color><color name="route_color">#2196F3</color><color name="card_background">#FFFFFF</color><color name="card_elevation">#E0E0E0</color><color name="button_primary">#2196F3</color><color name="button_secondary">#009688</color><color name="button_danger">#F44336</color><color name="button_disabled">#BDBDBD</color><color name="rating_star">#FFC107</color><color name="rating_star_empty">#E0E0E0</color><color name="transparent">#00000000</color><color name="semi_transparent_black">#80000000</color><color name="semi_transparent_white">#80FFFFFF</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="gray_50">#FAFAFA</color><color name="gray_100">#F5F5F5</color><color name="gray_200">#EEEEEE</color><color name="gray_300">#E0E0E0</color><color name="gray_400">#BDBDBD</color><color name="gray_500">#9E9E9E</color><color name="gray_600">#757575</color><color name="gray_700">#616161</color><color name="gray_800">#424242</color><color name="gray_900">#212121</color><color name="ic_launcher_background">#2196F3</color></file><file path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">QuickRide</string><string name="welcome_title">Welcome to QuickRide</string><string name="welcome_subtitle">Your reliable ride partner</string><string name="phone_number">Phone Number</string><string name="enter_phone">Enter your phone number</string><string name="send_otp">Send OTP</string><string name="verify_otp">Verify OTP</string><string name="enter_otp">Enter OTP</string><string name="resend_otp">Resend OTP</string><string name="full_name">Full Name</string><string name="enter_name">Enter your full name</string><string name="register">Register</string><string name="login">Login</string><string name="where_to">Where to?</string><string name="pickup_location">Pickup Location</string><string name="drop_location">Drop Location</string><string name="book_ride">Book Ride</string><string name="searching_drivers">Searching for drivers...</string><string name="driver_found">Driver Found!</string><string name="driver_arriving">Driver is arriving</string><string name="trip_started">Trip Started</string><string name="trip_completed">Trip Completed</string><string name="driver_name">Driver: %s</string><string formatted="false" name="vehicle_info">%s - %s</string><string name="driver_rating">Rating: %.1f</string><string name="eta">ETA: %d min</string><string name="distance">Distance: %.1f km</string><string name="estimated_fare">Estimated Fare</string><string name="final_fare">Final Fare</string><string name="fare_amount">₹%.0f</string><string name="payment_method">Payment Method</string><string name="cash">Cash</string><string name="upi">UPI</string><string name="card">Card</string><string name="home">Home</string><string name="history">History</string><string name="profile">Profile</string><string name="settings">Settings</string><string name="logout">Logout</string><string name="ride_history">Ride History</string><string name="no_rides">No rides yet</string><string name="ride_date">%s</string><string formatted="false" name="ride_route">%s to %s</string><string name="ride_fare">₹%.0f</string><string name="rate_ride">Rate this ride</string><string name="submit_rating">Submit Rating</string><string name="edit_profile">Edit Profile</string><string name="save_changes">Save Changes</string><string name="change_photo">Change Photo</string><string name="phone_verified">Phone Verified</string><string name="email_verified">Email Verified</string><string name="location_permission_title">Location Permission Required</string><string name="location_permission_message">QuickRide needs location access to find nearby drivers and track your ride.</string><string name="grant_permission">Grant Permission</string><string name="permission_denied">Permission Denied</string><string name="error_network">Network error. Please check your connection.</string><string name="error_location">Unable to get your location</string><string name="error_no_drivers">No drivers available in your area</string><string name="error_ride_cancelled">Ride was cancelled</string><string name="error_payment_failed">Payment failed. Please try again.</string><string name="error_invalid_phone">Please enter a valid phone number</string><string name="error_invalid_otp">Invalid OTP. Please try again.</string><string name="cancel">Cancel</string><string name="ok">OK</string><string name="retry">Retry</string><string name="call_driver">Call Driver</string><string name="cancel_ride">Cancel Ride</string><string name="confirm_cancellation">Are you sure you want to cancel this ride?</string><string name="loading">Loading...</string><string name="please_wait">Please wait...</string><string name="ride_booked">Ride booked successfully!</string><string name="payment_successful">Payment successful!</string><string name="profile_updated">Profile updated successfully!</string><string name="rating_submitted">Thank you for your feedback!</string></file><file path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.QuickRideCustomer" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        
        
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        
        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        
        
        <item name="android:statusBarColor">@color/primary_variant</item>
        <item name="android:windowLightStatusBar">false</item>
        
        
        <item name="android:navigationBarColor">@color/background</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        
        <item name="colorPrimaryDark">@color/primary_dark</item>
    </style><style name="Theme.QuickRideCustomer.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.QuickRideCustomer.Splash" parent="Theme.QuickRideCustomer.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style><style name="Button.Primary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/button_primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:minHeight">48dp</item>
    </style><style name="Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/button_secondary</item>
        <item name="android:textColor">@color/button_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:minHeight">48dp</item>
    </style><style name="Button.Danger" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/button_danger</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:minHeight">48dp</item>
    </style><style name="TextInputLayout.Primary" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
        <item name="android:textColorHint">@color/gray_600</item>
    </style><style name="Card.Primary" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style><style name="Text.Headline" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/on_background</item>
        <item name="android:textStyle">bold</item>
    </style><style name="Text.Title" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textColor">@color/on_background</item>
        <item name="android:textStyle">bold</item>
    </style><style name="Text.Body" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/on_background</item>
    </style><style name="Text.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/on_surface_variant</item>
    </style><style name="BottomNavigation.Primary" parent="Widget.Material3.BottomNavigationView">
        <item name="itemIconTint">@color/primary</item>
        <item name="itemTextColor">@color/primary</item>
        <item name="android:background">@color/surface</item>
        <item name="elevation">8dp</item>
    </style><style name="Toolbar.Primary" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/primary</item>
        <item name="titleTextColor">@color/white</item>
        <item name="subtitleTextColor">@color/white</item>
        <item name="android:theme">@style/ThemeOverlay.Material3.Dark.ActionBar</item>
    </style><style name="CheckoutTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/secondary</item>
    </style></file><file name="ic_launcher_foreground" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\mipmap-hdpi\ic_launcher.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\mipmap-hdpi\ic_launcher_round.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\mipmap-mdpi\ic_launcher.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\mipmap-mdpi\ic_launcher_round.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\mipmap-xhdpi\ic_launcher.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\mipmap-xhdpi\ic_launcher_round.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\mipmap-xxhdpi\ic_launcher.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\mipmap-xxxhdpi\ic_launcher.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="backup_rules" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>