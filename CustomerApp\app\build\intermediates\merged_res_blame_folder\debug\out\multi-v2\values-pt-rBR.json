{"logs": [{"outputFile": "com.quickride.customer.app-mergeDebugResources-79:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a80351878203576e14d29104a2365afc\\transformed\\play-services-basement-18.1.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5718", "endColumns": "144", "endOffsets": "5858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f7c442f9f9f43b1a617e6ca6fe146292\\transformed\\browser-1.7.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "68,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "6968,7521,7620,7732", "endColumns": "114,98,111,105", "endOffsets": "7078,7615,7727,7833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f8d4be1a1c7480e23037799cb5e0b535\\transformed\\material3-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4647,4736,4837,4917,5001,5102,5208,5300,5399,5487,5599,5700,5804,5923,6003,6103", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4642,4731,4832,4912,4996,5097,5203,5295,5394,5482,5594,5695,5799,5918,5998,6098,6190"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8157,8276,8397,8513,8629,8731,8828,8942,9076,9194,9346,9430,9531,9626,9726,9841,9971,10077,10216,10352,10483,10649,10776,10896,11020,11140,11236,11333,11453,11569,11669,11780,11889,12029,12174,12284,12387,12473,12567,12659,12749,12838,12939,13019,13103,13204,13310,13402,13501,13589,13701,13802,13906,14025,14105,14205", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "8271,8392,8508,8624,8726,8823,8937,9071,9189,9341,9425,9526,9621,9721,9836,9966,10072,10211,10347,10478,10644,10771,10891,11015,11135,11231,11328,11448,11564,11664,11775,11884,12024,12169,12279,12382,12468,12562,12654,12744,12833,12934,13014,13098,13199,13305,13397,13496,13584,13696,13797,13901,14020,14100,14200,14292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4a7df0f92ff33183244383dbcbaff3f8\\transformed\\foundation-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "20088,20171", "endColumns": "82,84", "endOffsets": "20166,20251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\471cdef36052226776d107dcedf318a4\\transformed\\play-services-base-18.1.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4706,4811,4959,5086,5194,5361,5491,5613,5863,6033,6141,6305,6435,6592,6749,6818,6884", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "4806,4954,5081,5189,5356,5486,5608,5713,6028,6136,6300,6430,6587,6744,6813,6879,6963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7dbe9381c9d479d3e9bf94a1e5011545\\transformed\\play-services-wallet-18.1.3\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "206", "endColumns": "75", "endOffsets": "281"}, "to": {"startLines": "209", "startColumns": "4", "startOffsets": "20256", "endColumns": "79", "endOffsets": "20331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7b90347aefdb8277a15a649c7d5249c4\\transformed\\ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "48,49,69,70,71,78,79,194,195,196,197,199,200,202,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4525,4620,7083,7180,7279,7917,8000,18998,19089,19176,19248,19394,19479,19655,19832,19908,19975", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4615,4701,7175,7274,7360,7995,8092,19084,19171,19243,19312,19474,19564,19726,19903,19970,20083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a207269cd93e115977cde60dd2975130\\transformed\\material-1.11.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1066,1158,1237,1297,1387,1451,1522,1585,1660,1724,1778,1905,1963,2025,2079,2158,2299,2386,2468,2607,2690,2774,2913,3000,3080,3136,3187,3253,3327,3407,3494,3577,3650,3727,3796,3870,3972,4060,4137,4230,4326,4400,4480,4577,4629,4713,4779,4866,4954,5016,5080,5143,5211,5323,5434,5541,5651,5711,5766", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "268,349,427,511,606,695,796,916,997,1061,1153,1232,1292,1382,1446,1517,1580,1655,1719,1773,1900,1958,2020,2074,2153,2294,2381,2463,2602,2685,2769,2908,2995,3075,3131,3182,3248,3322,3402,3489,3572,3645,3722,3791,3865,3967,4055,4132,4225,4321,4395,4475,4572,4624,4708,4774,4861,4949,5011,5075,5138,5206,5318,5429,5536,5646,5706,5761,5838"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,72,73,77,80,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4223,4324,4444,7365,7429,7838,8097,14297,14387,14451,14522,14585,14660,14724,14778,14905,14963,15025,15079,15158,15299,15386,15468,15607,15690,15774,15913,16000,16080,16136,16187,16253,16327,16407,16494,16577,16650,16727,16796,16870,16972,17060,17137,17230,17326,17400,17480,17577,17629,17713,17779,17866,17954,18016,18080,18143,18211,18323,18434,18541,18651,18711,19317", "endLines": "5,33,34,35,36,37,45,46,47,72,73,77,80,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,198", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "318,3137,3215,3299,3394,3483,4319,4439,4520,7424,7516,7912,8152,14382,14446,14517,14580,14655,14719,14773,14900,14958,15020,15074,15153,15294,15381,15463,15602,15685,15769,15908,15995,16075,16131,16182,16248,16322,16402,16489,16572,16645,16722,16791,16865,16967,17055,17132,17225,17321,17395,17475,17572,17624,17708,17774,17861,17949,18011,18075,18138,18206,18318,18429,18536,18646,18706,18761,19389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3e85b7c3ad0df798520dc2031c12bb41\\transformed\\appcompat-1.6.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,19569", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,19650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\83e5e2b178069d0cc8cbffee7b842817\\transformed\\navigation-ui-2.7.6\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "192,193", "startColumns": "4,4", "startOffsets": "18766,18878", "endColumns": "111,119", "endOffsets": "18873,18993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1347aa300b36a7cab68a2bd9530e812a\\transformed\\core-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,203", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3993,4103,19731", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3580,3682,3781,3881,3988,4098,4218,19827"}}]}]}