{"logs": [{"outputFile": "com.quickride.customer.app-mergeDebugResources-79:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f38c0f79029b1a7c880933489cb9e7c8\\transformed\\core-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,203", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3383,3479,3581,3678,3776,3883,3992,19189", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3474,3576,3673,3771,3878,3987,4105,19285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6fe1a530a34a585961db8e2456a941bf\\transformed\\material-1.11.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1072,1158,1231,1291,1378,1442,1504,1566,1634,1699,1755,1873,1931,1992,2048,2123,2249,2335,2415,2556,2634,2714,2836,2922,3000,3056,3107,3173,3241,3315,3404,3479,3551,3629,3699,3772,3876,3960,4037,4125,4214,4288,4361,4446,4495,4573,4639,4719,4802,4864,4928,4991,5060,5168,5271,5372,5471,5531,5586", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,121,85,77,55,50,65,67,73,88,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79", "endOffsets": "267,345,421,499,596,676,776,925,1003,1067,1153,1226,1286,1373,1437,1499,1561,1629,1694,1750,1868,1926,1987,2043,2118,2244,2330,2410,2551,2629,2709,2831,2917,2995,3051,3102,3168,3236,3310,3399,3474,3546,3624,3694,3767,3871,3955,4032,4120,4209,4283,4356,4441,4490,4568,4634,4714,4797,4859,4923,4986,5055,5163,5266,5367,5466,5526,5581,5661"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,72,73,77,80,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2974,3052,3128,3206,3303,4110,4210,4359,7199,7263,7652,7891,13984,14071,14135,14197,14259,14327,14392,14448,14566,14624,14685,14741,14816,14942,15028,15108,15249,15327,15407,15529,15615,15693,15749,15800,15866,15934,16008,16097,16172,16244,16322,16392,16465,16569,16653,16730,16818,16907,16981,17054,17139,17188,17266,17332,17412,17495,17557,17621,17684,17753,17861,17964,18065,18164,18224,18794", "endLines": "5,33,34,35,36,37,45,46,47,72,73,77,80,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,198", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,121,85,77,55,50,65,67,73,88,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79", "endOffsets": "317,3047,3123,3201,3298,3378,4205,4354,4432,7258,7344,7720,7946,14066,14130,14192,14254,14322,14387,14443,14561,14619,14680,14736,14811,14937,15023,15103,15244,15322,15402,15524,15610,15688,15744,15795,15861,15929,16003,16092,16167,16239,16317,16387,16460,16564,16648,16725,16813,16902,16976,17049,17134,17183,17261,17327,17407,17490,17552,17616,17679,17748,17856,17959,18060,18159,18219,18274,18869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ee31554eea207ebc8a9edcfcb545012b\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "48,49,69,70,71,78,79,194,195,196,197,199,200,202,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4437,4529,6923,7018,7117,7725,7802,18493,18582,18664,18729,18874,18955,19119,19290,19368,19435", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "4524,4604,7013,7112,7194,7797,7886,18577,18659,18724,18789,18950,19034,19184,19363,19430,19550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c448a021d6cf081194a9a8b36cfbe947\\transformed\\play-services-basement-18.1.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5604", "endColumns": "126", "endOffsets": "5726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86667447c7f3dd92ac1b3321380b62a6\\transformed\\material3-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4546,4631,4732,4812,4896,4997,5096,5191,5291,5378,5483,5585,5690,5807,5887,5989", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4541,4626,4727,4807,4891,4992,5091,5186,5286,5373,5478,5580,5685,5802,5882,5984,6083"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7951,8067,8180,8287,8401,8501,8596,8708,8852,8974,9123,9207,9307,9396,9490,9604,9722,9827,9952,10072,10208,10381,10511,10628,10750,10869,10959,11057,11176,11312,11410,11528,11630,11756,11889,11994,12092,12172,12265,12358,12442,12527,12628,12708,12792,12893,12992,13087,13187,13274,13379,13481,13586,13703,13783,13885", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "8062,8175,8282,8396,8496,8591,8703,8847,8969,9118,9202,9302,9391,9485,9599,9717,9822,9947,10067,10203,10376,10506,10623,10745,10864,10954,11052,11171,11307,11405,11523,11625,11751,11884,11989,12087,12167,12260,12353,12437,12522,12623,12703,12787,12888,12987,13082,13182,13269,13374,13476,13581,13698,13778,13880,13979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fa748d4bb156f3caf61873492a5b1b98\\transformed\\navigation-ui-2.7.6\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,108", "endOffsets": "155,264"}, "to": {"startLines": "192,193", "startColumns": "4,4", "startOffsets": "18279,18384", "endColumns": "104,108", "endOffsets": "18379,18488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\23d8f5487c8d20d85d38a43defb2ea01\\transformed\\play-services-base-18.1.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4609,4715,4875,5002,5111,5254,5379,5499,5731,5887,5993,6155,6282,6427,6605,6671,6733", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "4710,4870,4997,5106,5249,5374,5494,5599,5882,5988,6150,6277,6422,6600,6666,6728,6806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f2cfb53aefdccb4be79d4c4359b272f2\\transformed\\play-services-wallet-18.1.3\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "69", "endOffsets": "271"}, "to": {"startLines": "209", "startColumns": "4", "startOffsets": "19732", "endColumns": "73", "endOffsets": "19801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\52b456da0b71c6a7bafe9dda892dd878\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "19555,19645", "endColumns": "89,86", "endOffsets": "19640,19727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1a38c740f714c73665bde6d5b33bf438\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,19039", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,19114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eccd6ece3978e3682bca466c13afaf0a\\transformed\\browser-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "68,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "6811,7349,7448,7555", "endColumns": "111,98,106,96", "endOffsets": "6918,7443,7550,7647"}}]}]}