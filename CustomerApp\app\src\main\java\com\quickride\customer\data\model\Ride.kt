package com.quickride.customer.data.model

import kotlinx.serialization.Serializable

@Serializable
data class RideRequest(
    val id: String,
    val customerId: String,
    val driverId: String? = null,
    val pickupLocation: Location,
    val pickupAddress: String,
    val dropLocation: Location,
    val dropAddress: String,
    val estimatedFare: Double,
    val actualFare: Double? = null,
    val distanceKm: Double? = null,
    val estimatedDurationMinutes: Int? = null,
    val status: RideStatus = RideStatus.PENDING,
    val paymentMode: PaymentMode? = null,
    val specialInstructions: String? = null,
    val createdAt: String,
    val acceptedAt: String? = null,
    val startedAt: String? = null,
    val completedAt: String? = null,
    val cancelledAt: String? = null,
    val updatedAt: String
)

@Serializable
data class RideHistory(
    val id: String,
    val rideId: String,
    val customerId: String,
    val driverId: String,
    val customerRating: Int? = null,
    val driverRating: Int? = null,
    val customerFeedback: String? = null,
    val driverFeedback: String? = null,
    val paymentMode: PaymentMode,
    val finalFare: Double,
    val tipAmount: Double = 0.0,
    val createdAt: String
)

@Serializable
data class NearbyDriver(
    val driverId: String,
    val driverName: String,
    val vehicleNo: String,
    val vehicleModel: String? = null,
    val distanceKm: Double,
    val rating: Double,
    val locationLat: Double,
    val locationLon: Double
)

enum class RideStatus {
    PENDING, ACCEPTED, ONGOING, COMPLETED, CANCELLED
}

enum class PaymentMode {
    CASH, UPI, CARD, WALLET
}
