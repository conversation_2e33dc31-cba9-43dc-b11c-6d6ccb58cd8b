1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.quickride.customer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Internet permission for API calls -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Location permissions -->
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
16-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:10:5-79
16-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:10:22-76
17    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
17-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:11:5-81
17-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:11:22-78
18    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
18-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:12:5-85
18-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:12:22-82
19
20    <!-- Phone permission for authentication -->
21    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
21-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:15:5-75
21-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:15:22-72
22    <uses-permission android:name="android.permission.READ_SMS" />
22-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:16:5-67
22-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:16:22-64
23    <uses-permission android:name="android.permission.RECEIVE_SMS" />
23-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:17:5-70
23-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:17:22-67
24
25    <!-- Notification permissions -->
26    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
26-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:20:5-77
26-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:20:22-74
27    <uses-permission android:name="android.permission.VIBRATE" />
27-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:21:5-66
27-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:21:22-63
28
29    <!-- Camera permission for profile pictures -->
30    <uses-permission android:name="android.permission.CAMERA" />
30-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:24:5-65
30-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:24:22-62
31
32    <!-- Storage permissions -->
33    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
33-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:27:5-80
33-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:27:22-77
34    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
34-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:28:5-81
34-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:28:22-78
35
36    <queries>
36-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:10:5-39:15
37        <intent>
37-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:11:9-17:18
38            <action android:name="android.intent.action.VIEW" />
38-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:12:13-65
38-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:12:21-62
39
40            <data
40-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:14:13-16:38
41                android:mimeType="*/*"
41-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:15:17-39
42                android:scheme="*" />
42-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:16:17-35
43        </intent>
44        <intent>
44-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:18:9-27:18
45            <action android:name="android.intent.action.VIEW" />
45-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:12:13-65
45-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:12:21-62
46
47            <category android:name="android.intent.category.BROWSABLE" />
47-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:21:13-74
47-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:21:23-71
48
49            <data
49-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:14:13-16:38
50                android:host="pay"
51                android:mimeType="*/*"
51-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:15:17-39
52                android:scheme="upi" />
52-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:16:17-35
53        </intent>
54        <intent>
54-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:28:9-30:18
55            <action android:name="android.intent.action.MAIN" />
55-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:17-69
55-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:25-66
56        </intent>
57        <intent>
57-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:31:9-35:18
58            <action android:name="android.intent.action.SEND" />
58-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:32:13-65
58-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:32:21-62
59
60            <data android:mimeType="*/*" />
60-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:14:13-16:38
60-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:15:17-39
61        </intent>
62        <intent>
62-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:36:9-38:18
63            <action android:name="rzp.device_token.share" />
63-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:37:13-61
63-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:37:21-58
64        </intent>
65        <!-- Needs to be explicitly declared on Android R+ -->
66        <package android:name="com.google.android.apps.maps" />
66-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
66-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
67    </queries>
68
69    <uses-feature
69-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
70        android:glEsVersion="0x00020000"
70-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
71        android:required="true" />
71-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
72
73    <permission
73-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
74        android:name="com.quickride.customer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
74-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
75        android:protectionLevel="signature" />
75-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
76
77    <uses-permission android:name="com.quickride.customer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
77-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
77-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
78
79    <application
79-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:30:5-121:19
80        android:name="com.quickride.customer.QuickRideApplication"
80-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:31:9-45
81        android:allowBackup="true"
81-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:32:9-35
82        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
82-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
83        android:dataExtractionRules="@xml/data_extraction_rules"
83-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:33:9-65
84        android:debuggable="true"
85        android:extractNativeLibs="false"
86        android:fullBackupContent="@xml/backup_rules"
86-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:34:9-54
87        android:icon="@drawable/ic_launcher"
87-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:35:9-45
88        android:label="@string/app_name"
88-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:36:9-41
89        android:roundIcon="@drawable/ic_launcher"
89-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:37:9-50
90        android:supportsRtl="true"
90-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:38:9-35
91        android:theme="@style/Theme.QuickRideCustomer"
91-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:39:9-55
92        android:usesCleartextTraffic="true" >
92-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:40:9-44
93
94        <!-- Google Maps API Key -->
95        <meta-data
96            android:name="com.google.android.geo.API_KEY"
96-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:45:13-58
97            android:value="AIzaSyDummy_Key_Replace_With_Your_Actual_Key" />
97-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:46:13-44
98
99        <!-- Main Activity -->
100        <activity
100-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:49:9-58:20
101            android:name="com.quickride.customer.MainActivity"
101-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:50:13-41
102            android:exported="true"
102-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:51:13-36
103            android:label="@string/app_name"
103-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:52:13-45
104            android:theme="@style/Theme.QuickRideCustomer" >
104-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:53:13-59
105            <intent-filter>
105-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:54:13-57:29
106                <action android:name="android.intent.action.MAIN" />
106-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:17-69
106-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:25-66
107
108                <category android:name="android.intent.category.LAUNCHER" />
108-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:56:17-77
108-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:56:27-74
109            </intent-filter>
110        </activity>
111
112        <!-- Authentication Activity -->
113        <activity
113-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:61:9-64:74
114            android:name="com.quickride.customer.ui.auth.AuthActivity"
114-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:62:13-49
115            android:exported="false"
115-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:63:13-37
116            android:theme="@style/Theme.QuickRideCustomer.NoActionBar" />
116-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:64:13-71
117
118        <!-- Splash Activity -->
119        <activity
119-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:67:9-75:20
120            android:name="com.quickride.customer.ui.splash.SplashActivity"
120-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:68:13-53
121            android:exported="true"
121-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:69:13-36
122            android:theme="@style/Theme.QuickRideCustomer.Splash" >
122-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:70:13-66
123            <intent-filter>
123-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:54:13-57:29
124                <action android:name="android.intent.action.MAIN" />
124-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:17-69
124-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:25-66
125
126                <category android:name="android.intent.category.LAUNCHER" />
126-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:56:17-77
126-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:56:27-74
127            </intent-filter>
128        </activity>
129
130        <!-- Ride Activity -->
131        <activity
131-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:78:9-81:74
132            android:name="com.quickride.customer.ui.ride.RideActivity"
132-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:79:13-49
133            android:exported="false"
133-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:80:13-37
134            android:theme="@style/Theme.QuickRideCustomer.NoActionBar" />
134-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:81:13-71
135
136        <!-- Profile Activity -->
137        <activity
137-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:84:9-86:40
138            android:name="com.quickride.customer.ui.profile.ProfileActivity"
138-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:85:13-55
139            android:exported="false" />
139-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:86:13-37
140
141        <!-- History Activity -->
142        <activity
142-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:89:9-91:40
143            android:name="com.quickride.customer.ui.history.HistoryActivity"
143-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:90:13-55
144            android:exported="false" />
144-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:91:13-37
145
146        <!-- Payment Activity -->
147        <activity
147-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:94:9-96:40
148            android:name="com.quickride.customer.ui.payment.PaymentActivity"
148-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:95:13-55
149            android:exported="false" />
149-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:96:13-37
150
151        <!-- Location Service -->
152        <service
152-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:99:9-103:56
153            android:name="com.quickride.customer.service.LocationService"
153-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:100:13-52
154            android:enabled="true"
154-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:101:13-35
155            android:exported="false"
155-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:102:13-37
156            android:foregroundServiceType="location" />
156-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:103:13-53
157
158        <!-- Firebase Messaging Service -->
159        <service
159-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:106:9-112:19
160            android:name="com.quickride.customer.service.NotificationService"
160-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:107:13-56
161            android:exported="false" >
161-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:108:13-37
162            <intent-filter>
162-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:109:13-111:29
163                <action android:name="com.google.firebase.MESSAGING_EVENT" />
163-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:110:17-78
163-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:110:25-75
164            </intent-filter>
165        </service>
166
167        <!-- Razorpay Payment Activity -->
168        <activity
168-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:115:9-119:48
169            android:name="com.razorpay.CheckoutActivity"
169-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:116:13-57
170            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
170-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:53:13-83
171            android:exported="false"
171-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:117:13-37
172            android:theme="@style/CheckoutTheme" >
172-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:118:13-49
173            <intent-filter>
173-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:56:13-58:29
174                <action android:name="android.intent.action.MAIN" />
174-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:17-69
174-->C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:25-66
175            </intent-filter>
176        </activity>
177
178        <receiver
178-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:42:9-49:20
179            android:name="com.razorpay.RzpTokenReceiver"
179-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:43:13-57
180            android:exported="true" >
180-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:44:13-36
181            <intent-filter>
181-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:46:13-48:29
182                <action android:name="rzp.device_token.share" />
182-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:37:13-61
182-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:37:21-58
183            </intent-filter>
184        </receiver>
185
186        <meta-data
186-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:61:9-63:58
187            android:name="com.razorpay.plugin.googlepay_all"
187-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:62:13-61
188            android:value="com.razorpay.RzpGpayMerged" /> <!-- Needs to be explicitly declared on P+ -->
188-->[com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:63:13-55
189        <uses-library
189-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
190            android:name="org.apache.http.legacy"
190-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
191            android:required="false" />
191-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
192
193        <activity
193-->[com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:23:9-27:75
194            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
194-->[com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:24:13-93
195            android:excludeFromRecents="true"
195-->[com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:25:13-46
196            android:exported="false"
196-->[com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:26:13-37
197            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
197-->[com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:27:13-72
198        <!--
199            Service handling Google Sign-In user revocation. For apps that do not integrate with
200            Google Sign-In, this service will never be started.
201        -->
202        <service
202-->[com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:33:9-36:110
203            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
203-->[com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:34:13-89
204            android:exported="true"
204-->[com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:35:13-36
205            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION" />
205-->[com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:36:13-107
206
207        <activity
207-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
208            android:name="com.google.android.gms.common.api.GoogleApiActivity"
208-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
209            android:exported="false"
209-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
210            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
210-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
211
212        <meta-data
212-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
213            android:name="com.google.android.gms.version"
213-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
214            android:value="@integer/google_play_services_version" />
214-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
215
216        <activity
216-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
217            android:name="androidx.activity.ComponentActivity"
217-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
218            android:exported="true" />
218-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
219        <activity
219-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
220            android:name="androidx.compose.ui.tooling.PreviewActivity"
220-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
221            android:exported="true" />
221-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
222
223        <provider
223-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
224            android:name="androidx.startup.InitializationProvider"
224-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
225            android:authorities="com.quickride.customer.androidx-startup"
225-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
226            android:exported="false" >
226-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
227            <meta-data
227-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
228                android:name="androidx.emoji2.text.EmojiCompatInitializer"
228-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
229                android:value="androidx.startup" />
229-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
230            <meta-data
230-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
231                android:name="io.github.jan.supabase.gotrue.SupabaseInitializer"
231-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
232                android:value="androidx.startup" />
232-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
233            <meta-data
233-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
234                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
234-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
235                android:value="androidx.startup" />
235-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
236            <meta-data
236-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
237                android:name="com.russhwolf.settings.SettingsInitializer"
237-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
238                android:value="androidx.startup" />
238-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
239            <meta-data
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
240                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
241                android:value="androidx.startup" />
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
242        </provider>
243
244        <uses-library
244-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
245            android:name="androidx.window.extensions"
245-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
246            android:required="false" />
246-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
247        <uses-library
247-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
248            android:name="androidx.window.sidecar"
248-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
249            android:required="false" />
249-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
250
251        <receiver
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
252            android:name="androidx.profileinstaller.ProfileInstallReceiver"
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
253            android:directBootAware="false"
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
254            android:enabled="true"
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
255            android:exported="true"
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
256            android:permission="android.permission.DUMP" >
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
258                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
259            </intent-filter>
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
261                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
262            </intent-filter>
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
264                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
265            </intent-filter>
266            <intent-filter>
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
267                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
268            </intent-filter>
269        </receiver>
270    </application>
271
272</manifest>
