{"logs": [{"outputFile": "com.quickride.customer.app-mergeDebugResources-79:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6fe1a530a34a585961db8e2456a941bf\\transformed\\material-1.11.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1070,1159,1238,1301,1394,1456,1522,1580,1653,1717,1773,1895,1952,2014,2070,2146,2280,2365,2451,2589,2670,2749,2873,2963,3040,3097,3148,3214,3292,3375,3463,3539,3614,3693,3766,3837,3946,4040,4118,4207,4297,4371,4452,4539,4592,4671,4738,4819,4903,4965,5029,5092,5163,5271,5383,5485,5596,5657,5712", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,123,89,76,56,50,65,77,82,87,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80", "endOffsets": "264,350,432,509,607,701,798,920,1001,1065,1154,1233,1296,1389,1451,1517,1575,1648,1712,1768,1890,1947,2009,2065,2141,2275,2360,2446,2584,2665,2744,2868,2958,3035,3092,3143,3209,3287,3370,3458,3534,3609,3688,3761,3832,3941,4035,4113,4202,4292,4366,4447,4534,4587,4666,4733,4814,4898,4960,5024,5087,5158,5266,5378,5480,5591,5652,5707,5788"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,72,73,77,80,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3041,3127,3209,3286,3384,4212,4309,4431,7369,7433,7833,8084,14322,14415,14477,14543,14601,14674,14738,14794,14916,14973,15035,15091,15167,15301,15386,15472,15610,15691,15770,15894,15984,16061,16118,16169,16235,16313,16396,16484,16560,16635,16714,16787,16858,16967,17061,17139,17228,17318,17392,17473,17560,17613,17692,17759,17840,17924,17986,18050,18113,18184,18292,18404,18506,18617,18678,19259", "endLines": "5,33,34,35,36,37,45,46,47,72,73,77,80,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,198", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,123,89,76,56,50,65,77,82,87,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80", "endOffsets": "314,3122,3204,3281,3379,3473,4304,4426,4507,7428,7517,7907,8142,14410,14472,14538,14596,14669,14733,14789,14911,14968,15030,15086,15162,15296,15381,15467,15605,15686,15765,15889,15979,16056,16113,16164,16230,16308,16391,16479,16555,16630,16709,16782,16853,16962,17056,17134,17223,17313,17387,17468,17555,17608,17687,17754,17835,17919,17981,18045,18108,18179,18287,18399,18501,18612,18673,18728,19335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ee31554eea207ebc8a9edcfcb545012b\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "48,49,69,70,71,78,79,194,195,196,197,199,200,202,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4512,4604,7088,7185,7284,7912,7988,18953,19040,19129,19194,19340,19421,19587,19765,19849,19919", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "4599,4682,7180,7279,7364,7983,8079,19035,19124,19189,19254,19416,19499,19659,19844,19914,20034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\23d8f5487c8d20d85d38a43defb2ea01\\transformed\\play-services-base-18.1.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4687,4795,4946,5074,5185,5352,5479,5602,5851,6029,6135,6304,6430,6593,6775,6843,6906", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "4790,4941,5069,5180,5347,5474,5597,5703,6024,6130,6299,6425,6588,6770,6838,6901,6980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1a38c740f714c73665bde6d5b33bf438\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,19504", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,19582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eccd6ece3978e3682bca466c13afaf0a\\transformed\\browser-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "68,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "6985,7522,7623,7734", "endColumns": "102,100,110,98", "endOffsets": "7083,7618,7729,7828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f38c0f79029b1a7c880933489cb9e7c8\\transformed\\core-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,203", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3478,3580,3682,3782,3882,3989,4093,19664", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3575,3677,3777,3877,3984,4088,4207,19760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c448a021d6cf081194a9a8b36cfbe947\\transformed\\play-services-basement-18.1.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5708", "endColumns": "142", "endOffsets": "5846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\52b456da0b71c6a7bafe9dda892dd878\\transformed\\foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,85", "endOffsets": "136,222"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "20039,20125", "endColumns": "85,85", "endOffsets": "20120,20206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f2cfb53aefdccb4be79d4c4359b272f2\\transformed\\play-services-wallet-18.1.3\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "71", "endOffsets": "273"}, "to": {"startLines": "209", "startColumns": "4", "startOffsets": "20211", "endColumns": "75", "endOffsets": "20282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86667447c7f3dd92ac1b3321380b62a6\\transformed\\material3-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,415,533,633,731,846,998,1119,1261,1346,1445,1541,1644,1762,1883,1987,2118,2246,2382,2560,2691,2811,2932,3067,3164,3264,3384,3513,3613,3720,3823,3960,4100,4206,4310,4394,4494,4591,4678,4765,4870,4950,5033,5132,5236,5331,5430,5518,5628,5729,5834,5954,6034,6135", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "172,289,410,528,628,726,841,993,1114,1256,1341,1440,1536,1639,1757,1878,1982,2113,2241,2377,2555,2686,2806,2927,3062,3159,3259,3379,3508,3608,3715,3818,3955,4095,4201,4305,4389,4489,4586,4673,4760,4865,4945,5028,5127,5231,5326,5425,5513,5623,5724,5829,5949,6029,6130,6225"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8147,8269,8386,8507,8625,8725,8823,8938,9090,9211,9353,9438,9537,9633,9736,9854,9975,10079,10210,10338,10474,10652,10783,10903,11024,11159,11256,11356,11476,11605,11705,11812,11915,12052,12192,12298,12402,12486,12586,12683,12770,12857,12962,13042,13125,13224,13328,13423,13522,13610,13720,13821,13926,14046,14126,14227", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "8264,8381,8502,8620,8720,8818,8933,9085,9206,9348,9433,9532,9628,9731,9849,9970,10074,10205,10333,10469,10647,10778,10898,11019,11154,11251,11351,11471,11600,11700,11807,11910,12047,12187,12293,12397,12481,12581,12678,12765,12852,12957,13037,13120,13219,13323,13418,13517,13605,13715,13816,13921,14041,14121,14222,14317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fa748d4bb156f3caf61873492a5b1b98\\transformed\\navigation-ui-2.7.6\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,108", "endOffsets": "161,270"}, "to": {"startLines": "192,193", "startColumns": "4,4", "startOffsets": "18733,18844", "endColumns": "110,108", "endOffsets": "18839,18948"}}]}]}