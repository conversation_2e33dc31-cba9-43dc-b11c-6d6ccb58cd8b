package com.quickride.customer.data.repository

import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.gotrue.GoTrue
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.realtime.Realtime
import io.ktor.client.engine.android.*

class SupabaseRepository {
    
    companion object {
        private const val SUPABASE_URL = "https://rrvqtlnzzwmyowbeehqt.supabase.co"
        private const val SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJydnF0bG56endteW93YmVlaHF0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NDMyMTksImV4cCI6MjA2NjUxOTIxOX0.GqjhlBzRiY-nvG9H2nnH4XuXzZtsIEOMV2lcIxhdBbU"
    }
    
    val client = createSupabaseClient(
        supabaseUrl = SUPABASE_URL,
        supabaseKey = SUPABASE_ANON_KEY
    ) {
        install(GoTrue)
        install(Postgrest)
        install(Realtime)
        
        // Use Android HTTP client
        httpEngine = Android.create()
    }
    
    val auth = client.gotrue
    val database = client.postgrest
    val realtime = client.realtime
    
    fun initialize() {
        // Initialize Supabase client
        // Any additional setup can be done here
    }
}
