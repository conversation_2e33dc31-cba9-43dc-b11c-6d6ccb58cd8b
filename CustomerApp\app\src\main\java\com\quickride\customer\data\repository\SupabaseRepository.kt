package com.quickride.customer.data.repository

import com.quickride.customer.utils.Constants
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.gotrue.GoTrue
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.realtime.Realtime
import io.ktor.client.engine.android.*

class SupabaseRepository {

    val client = createSupabaseClient(
        supabaseUrl = Constants.SUPABASE_URL,
        supabaseKey = Constants.SUPABASE_ANON_KEY
    ) {
        install(GoTrue)
        install(Postgrest)
        install(Realtime)

        // Use Android HTTP client
        httpEngine = Android.create()
    }

    val auth = client.gotrue
    val database = client.postgrest
    val realtime = client.realtime

    fun initialize() {
        // Initialize Supabase client
        // Any additional setup can be done here
    }
}
