package com.quickride.customer.utils

object Constants {
    // Supabase Configuration
    const val SUPABASE_URL = "https://rrvqtlnzzwmyowbeehqt.supabase.co"
    const val SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJydnF0bG56endteW93YmVlaHF0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NDMyMTksImV4cCI6MjA2NjUxOTIxOX0.GqjhlBzRiY-nvG9H2nnH4XuXzZtsIEOMV2lcIxhdBbU"
    
    // Database Tables
    const val TABLE_USERS = "users"
    const val TABLE_DRIVERS = "drivers"
    const val TABLE_RIDE_REQUESTS = "ride_requests"
    const val TABLE_RIDE_HISTORY = "ride_history"
    const val TABLE_DRIVER_LOCATIONS = "driver_locations"
    const val TABLE_NOTIFICATIONS = "notifications"
    
    // Realtime Channels
    const val CHANNEL_RIDE_REQUESTS = "ride_requests"
    const val CHANNEL_DRIVER_LOCATIONS = "driver_locations"
    const val CHANNEL_NOTIFICATIONS = "notifications"
    
    // Shared Preferences
    const val PREF_NAME = "quickride_customer_prefs"
    const val PREF_USER_ID = "user_id"
    const val PREF_USER_NAME = "user_name"
    const val PREF_USER_PHONE = "user_phone"
    const val PREF_IS_LOGGED_IN = "is_logged_in"
    const val PREF_LAST_LOCATION_LAT = "last_location_lat"
    const val PREF_LAST_LOCATION_LNG = "last_location_lng"
    
    // Location
    const val LOCATION_UPDATE_INTERVAL = 5000L // 5 seconds
    const val LOCATION_FASTEST_INTERVAL = 2000L // 2 seconds
    const val DEFAULT_ZOOM = 15f
    
    // Ride
    const val DRIVER_SEARCH_RADIUS_KM = 10.0
    const val MAX_DRIVERS_TO_SHOW = 5
    const val RIDE_REQUEST_TIMEOUT_SECONDS = 30
    
    // Fare Calculation
    const val BASE_FARE = 50.0
    const val PER_KM_RATE = 12.0
    const val MINIMUM_FARE = 80.0
    
    // Payment
    const val RAZORPAY_KEY_ID = "rzp_test_1234567890" // Replace with actual key
    
    // Notification
    const val NOTIFICATION_CHANNEL_ID = "quickride_notifications"
    const val NOTIFICATION_CHANNEL_NAME = "QuickRide Notifications"
    
    // Request Codes
    const val REQUEST_CODE_LOCATION_PERMISSION = 1001
    const val REQUEST_CODE_PHONE_PERMISSION = 1002
    const val REQUEST_CODE_CAMERA_PERMISSION = 1003
    const val REQUEST_CODE_STORAGE_PERMISSION = 1004
    
    // Intent Extras
    const val EXTRA_RIDE_ID = "ride_id"
    const val EXTRA_DRIVER_ID = "driver_id"
    const val EXTRA_PICKUP_LAT = "pickup_lat"
    const val EXTRA_PICKUP_LNG = "pickup_lng"
    const val EXTRA_DROP_LAT = "drop_lat"
    const val EXTRA_DROP_LNG = "drop_lng"
}
