@echo off
echo ========================================
echo QuickRide Database Setup Script
echo ========================================
echo.

echo Checking if <PERSON><PERSON> is running...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not running.
    echo Please install Docker Desktop from: https://docs.docker.com/desktop/
    echo.
    echo Alternative: Use Supabase Cloud
    echo 1. Go to https://supabase.com
    echo 2. Create a new project
    echo 3. Run the SQL scripts in database/setup_complete.sql
    echo 4. Follow instructions in database/run_setup.md
    pause
    exit /b 1
)

echo Docker is available. Starting Supabase local development...
echo.

echo Step 1: Starting Supabase services...
npx supabase start

if %errorlevel% neq 0 (
    echo ERROR: Failed to start Supabase services.
    echo Make sure Docker Desktop is running and try again.
    pause
    exit /b 1
)

echo.
echo Step 2: Running database schema setup...
npx supabase db reset

echo.
echo Step 3: Applying custom schema and functions...
npx supabase db push

echo.
echo ========================================
echo Database setup completed successfully!
echo ========================================
echo.
echo Your local Supabase instance is running at:
echo API URL: http://localhost:54321
echo Dashboard: http://localhost:54323
echo.
echo To stop the services, run: npx supabase stop
echo.
pause
