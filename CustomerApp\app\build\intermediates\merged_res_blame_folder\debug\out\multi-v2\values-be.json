{"logs": [{"outputFile": "com.quickride.customer.app-mergeDebugResources-79:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1347aa300b36a7cab68a2bd9530e812a\\transformed\\core-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,205", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3587,3685,3787,3887,3988,4094,4197,19924", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3680,3782,3882,3983,4089,4192,4313,20020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\83e5e2b178069d0cc8cbffee7b842817\\transformed\\navigation-ui-2.7.6\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,125", "endOffsets": "159,285"}, "to": {"startLines": "194,195", "startColumns": "4,4", "startOffsets": "18974,19083", "endColumns": "108,125", "endOffsets": "19078,19204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7dbe9381c9d479d3e9bf94a1e5011545\\transformed\\play-services-wallet-18.1.3\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "80", "endOffsets": "282"}, "to": {"startLines": "211", "startColumns": "4", "startOffsets": "20493", "endColumns": "84", "endOffsets": "20573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4a7df0f92ff33183244383dbcbaff3f8\\transformed\\foundation-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,102", "endOffsets": "137,240"}, "to": {"startLines": "209,210", "startColumns": "4,4", "startOffsets": "20303,20390", "endColumns": "86,102", "endOffsets": "20385,20488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f7c442f9f9f43b1a617e6ca6fe146292\\transformed\\browser-1.7.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,106", "endOffsets": "159,267,379,486"}, "to": {"startLines": "70,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "7039,7591,7699,7811", "endColumns": "108,107,111,106", "endOffsets": "7143,7694,7806,7913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f8d4be1a1c7480e23037799cb5e0b535\\transformed\\material3-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,621,718,832,955,1070,1215,1299,1410,1503,1600,1714,1837,1953,2100,2246,2384,2561,2693,2818,2947,3069,3163,3261,3387,3520,3619,3730,3839,3989,4142,4250,4350,4435,4530,4626,4712,4799,4899,4986,5073,5173,5279,5375,5473,5562,5670,5766,5866,6012,6102,6220", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "167,282,401,518,616,713,827,950,1065,1210,1294,1405,1498,1595,1709,1832,1948,2095,2241,2379,2556,2688,2813,2942,3064,3158,3256,3382,3515,3614,3725,3834,3984,4137,4245,4345,4430,4525,4621,4707,4794,4894,4981,5068,5168,5274,5370,5468,5557,5665,5761,5861,6007,6097,6215,6311"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8227,8344,8459,8578,8695,8793,8890,9004,9127,9242,9387,9471,9582,9675,9772,9886,10009,10125,10272,10418,10556,10733,10865,10990,11119,11241,11335,11433,11559,11692,11791,11902,12011,12161,12314,12422,12522,12607,12702,12798,12884,12971,13071,13158,13245,13345,13451,13547,13645,13734,13842,13938,14038,14184,14274,14392", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "8339,8454,8573,8690,8788,8885,8999,9122,9237,9382,9466,9577,9670,9767,9881,10004,10120,10267,10413,10551,10728,10860,10985,11114,11236,11330,11428,11554,11687,11786,11897,12006,12156,12309,12417,12517,12602,12697,12793,12879,12966,13066,13153,13240,13340,13446,13542,13640,13729,13837,13933,14033,14179,14269,14387,14483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3e85b7c3ad0df798520dc2031c12bb41\\transformed\\appcompat-1.6.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,19770", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,19847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\471cdef36052226776d107dcedf318a4\\transformed\\play-services-base-18.1.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4807,4914,5076,5201,5311,5466,5592,5707,5957,6119,6226,6389,6517,6670,6829,6898,6960", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "4909,5071,5196,5306,5461,5587,5702,5806,6114,6221,6384,6512,6665,6824,6893,6955,7034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a207269cd93e115977cde60dd2975130\\transformed\\material-1.11.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1186,1279,1356,1419,1535,1598,1667,1726,1797,1856,1910,2031,2092,2155,2209,2282,2404,2492,2575,2727,2813,2900,3033,3124,3207,3264,3315,3381,3453,3530,3614,3697,3772,3849,3931,4007,4115,4204,4286,4377,4473,4547,4628,4723,4777,4859,4925,5012,5098,5160,5224,5287,5356,5466,5579,5682,5789,5850,5905", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1181,1274,1351,1414,1530,1593,1662,1721,1792,1851,1905,2026,2087,2150,2204,2277,2399,2487,2570,2722,2808,2895,3028,3119,3202,3259,3310,3376,3448,3525,3609,3692,3767,3844,3926,4002,4110,4199,4281,4372,4468,4542,4623,4718,4772,4854,4920,5007,5093,5155,5219,5282,5351,5461,5574,5677,5784,5845,5900,5980"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,74,75,79,82,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3162,3239,3316,3398,3495,4318,4415,4547,7431,7498,7918,8164,14488,14604,14667,14736,14795,14866,14925,14979,15100,15161,15224,15278,15351,15473,15561,15644,15796,15882,15969,16102,16193,16276,16333,16384,16450,16522,16599,16683,16766,16841,16918,17000,17076,17184,17273,17355,17446,17542,17616,17697,17792,17846,17928,17994,18081,18167,18229,18293,18356,18425,18535,18648,18751,18858,18919,19520", "endLines": "7,35,36,37,38,39,47,48,49,74,75,79,82,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,200", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "427,3234,3311,3393,3490,3582,4410,4542,4625,7493,7586,7990,8222,14599,14662,14731,14790,14861,14920,14974,15095,15156,15219,15273,15346,15468,15556,15639,15791,15877,15964,16097,16188,16271,16328,16379,16445,16517,16594,16678,16761,16836,16913,16995,17071,17179,17268,17350,17441,17537,17611,17692,17787,17841,17923,17989,18076,18162,18224,18288,18351,18420,18530,18643,18746,18853,18914,18969,19595"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a80351878203576e14d29104a2365afc\\transformed\\play-services-basement-18.1.0\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5811", "endColumns": "145", "endOffsets": "5952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7b90347aefdb8277a15a649c7d5249c4\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,975,1045,1128,1215,1287,1372,1442", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,970,1040,1123,1210,1282,1367,1437,1560"}, "to": {"startLines": "50,51,71,72,73,80,81,196,197,198,199,201,202,204,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4630,4723,7148,7242,7345,7995,8075,19209,19297,19379,19450,19600,19683,19852,20025,20110,20180", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "4718,4802,7237,7340,7426,8070,8159,19292,19374,19445,19515,19678,19765,19919,20105,20175,20298"}}]}]}