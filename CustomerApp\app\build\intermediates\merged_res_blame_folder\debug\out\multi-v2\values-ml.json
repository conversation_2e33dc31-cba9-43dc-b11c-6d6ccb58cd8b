{"logs": [{"outputFile": "com.quickride.customer.app-mergeDebugResources-79:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f38c0f79029b1a7c880933489cb9e7c8\\transformed\\core-1.12.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "38,39,40,41,42,43,44,203", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3525,3627,3730,3832,3936,4039,4140,20229", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3622,3725,3827,3931,4034,4135,4257,20325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c448a021d6cf081194a9a8b36cfbe947\\transformed\\play-services-basement-18.1.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5798", "endColumns": "159", "endOffsets": "5953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6fe1a530a34a585961db8e2456a941bf\\transformed\\material-1.11.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,351,434,521,627,726,820,930,1022,1087,1186,1252,1312,1414,1476,1552,1610,1688,1753,1807,1924,1988,2052,2106,2186,2320,2406,2495,2631,2716,2804,2956,3051,3134,3192,3244,3310,3389,3471,3562,3649,3725,3802,3879,3950,4060,4167,4247,4344,4444,4518,4599,4704,4762,4850,4917,5008,5100,5162,5226,5289,5358,5461,5568,5673,5778,5840,5896", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,82,86,105,98,93,109,91,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,88,135,84,87,151,94,82,57,51,65,78,81,90,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83", "endOffsets": "266,346,429,516,622,721,815,925,1017,1082,1181,1247,1307,1409,1471,1547,1605,1683,1748,1802,1919,1983,2047,2101,2181,2315,2401,2490,2626,2711,2799,2951,3046,3129,3187,3239,3305,3384,3466,3557,3644,3720,3797,3874,3945,4055,4162,4242,4339,4439,4513,4594,4699,4757,4845,4912,5003,5095,5157,5221,5284,5353,5456,5563,5668,5773,5835,5891,5975"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,72,73,77,80,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3150,3233,3320,3426,4262,4356,4466,7503,7568,7985,8238,14674,14776,14838,14914,14972,15050,15115,15169,15286,15350,15414,15468,15548,15682,15768,15857,15993,16078,16166,16318,16413,16496,16554,16606,16672,16751,16833,16924,17011,17087,17164,17241,17312,17422,17529,17609,17706,17806,17880,17961,18066,18124,18212,18279,18370,18462,18524,18588,18651,18720,18823,18930,19035,19140,19202,19811", "endLines": "5,33,34,35,36,37,45,46,47,72,73,77,80,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,198", "endColumns": "12,79,82,86,105,98,93,109,91,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,88,135,84,87,151,94,82,57,51,65,78,81,90,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83", "endOffsets": "316,3145,3228,3315,3421,3520,4351,4461,4553,7563,7662,8046,8293,14771,14833,14909,14967,15045,15110,15164,15281,15345,15409,15463,15543,15677,15763,15852,15988,16073,16161,16313,16408,16491,16549,16601,16667,16746,16828,16919,17006,17082,17159,17236,17307,17417,17524,17604,17701,17801,17875,17956,18061,18119,18207,18274,18365,18457,18519,18583,18646,18715,18818,18925,19030,19135,19197,19253,19890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ee31554eea207ebc8a9edcfcb545012b\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1009,1076,1162,1249,1327,1403,1470", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1004,1071,1157,1244,1322,1398,1465,1584"}, "to": {"startLines": "48,49,69,70,71,78,79,194,195,196,197,199,200,202,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4558,4653,7210,7309,7413,8051,8137,19502,19589,19677,19744,19895,19981,20151,20330,20406,20473", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "4648,4735,7304,7408,7498,8132,8233,19584,19672,19739,19806,19976,20063,20224,20401,20468,20587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1a38c740f714c73665bde6d5b33bf438\\transformed\\appcompat-1.6.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,428,534,645,736,841,963,1041,1116,1207,1300,1401,1495,1595,1689,1784,1883,1974,2065,2147,2256,2360,2459,2571,2683,2804,2969,20068", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "423,529,640,731,836,958,1036,1111,1202,1295,1396,1490,1590,1684,1779,1878,1969,2060,2142,2251,2355,2454,2566,2678,2799,2964,3065,20146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eccd6ece3978e3682bca466c13afaf0a\\transformed\\browser-1.7.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,103", "endOffsets": "159,262,373,477"}, "to": {"startLines": "68,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7101,7667,7770,7881", "endColumns": "108,102,110,103", "endOffsets": "7205,7765,7876,7980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\23d8f5487c8d20d85d38a43defb2ea01\\transformed\\play-services-base-18.1.0\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4740,4859,5038,5177,5298,5462,5587,5692,5958,6140,6256,6430,6566,6715,6876,6940,7009", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "4854,5033,5172,5293,5457,5582,5687,5793,6135,6251,6425,6561,6710,6871,6935,7004,7096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\52b456da0b71c6a7bafe9dda892dd878\\transformed\\foundation-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,91", "endOffsets": "138,230"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "20592,20680", "endColumns": "87,91", "endOffsets": "20675,20767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fa748d4bb156f3caf61873492a5b1b98\\transformed\\navigation-ui-2.7.6\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,180", "endColumns": "124,118", "endOffsets": "175,294"}, "to": {"startLines": "192,193", "startColumns": "4,4", "startOffsets": "19258,19383", "endColumns": "124,118", "endOffsets": "19378,19497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f2cfb53aefdccb4be79d4c4359b272f2\\transformed\\play-services-wallet-18.1.3\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "73", "endOffsets": "275"}, "to": {"startLines": "209", "startColumns": "4", "startOffsets": "20772", "endColumns": "77", "endOffsets": "20845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86667447c7f3dd92ac1b3321380b62a6\\transformed\\material3-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,301,427,550,650,744,855,1007,1125,1282,1367,1472,1572,1674,1797,1930,2040,2176,2318,2449,2653,2787,2911,3041,3175,3276,3374,3492,3623,3722,3824,3937,4075,4221,4335,4444,4520,4618,4718,4805,4902,5010,5090,5178,5276,5389,5484,5595,5685,5800,5902,6015,6147,6227,6334", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "170,296,422,545,645,739,850,1002,1120,1277,1362,1467,1567,1669,1792,1925,2035,2171,2313,2444,2648,2782,2906,3036,3170,3271,3369,3487,3618,3717,3819,3932,4070,4216,4330,4439,4515,4613,4713,4800,4897,5005,5085,5173,5271,5384,5479,5590,5680,5795,5897,6010,6142,6222,6329,6426"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8298,8418,8544,8670,8793,8893,8987,9098,9250,9368,9525,9610,9715,9815,9917,10040,10173,10283,10419,10561,10692,10896,11030,11154,11284,11418,11519,11617,11735,11866,11965,12067,12180,12318,12464,12578,12687,12763,12861,12961,13048,13145,13253,13333,13421,13519,13632,13727,13838,13928,14043,14145,14258,14390,14470,14577", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "8413,8539,8665,8788,8888,8982,9093,9245,9363,9520,9605,9710,9810,9912,10035,10168,10278,10414,10556,10687,10891,11025,11149,11279,11413,11514,11612,11730,11861,11960,12062,12175,12313,12459,12573,12682,12758,12856,12956,13043,13140,13248,13328,13416,13514,13627,13722,13833,13923,14038,14140,14253,14385,14465,14572,14669"}}]}]}