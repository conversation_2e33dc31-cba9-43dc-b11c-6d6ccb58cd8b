package com.quickride.driver.ui.theme

import androidx.compose.ui.graphics.Color

// QuickRide Driver Brand Colors (Dark Theme)
val Primary = Color(0xFFFFC107) // Yellow/Gold
val PrimaryVariant = Color(0xFFFF8F00)
val Secondary = Color(0xFF607D8B) // Blue Grey
val SecondaryVariant = Color(0xFF455A64)
val Accent = Color(0xFFFF5722) // Deep Orange

// Background Colors (Dark Theme)
val Background = Color(0xFF121212)
val Surface = Color(0xFF1E1E1E)
val SurfaceVariant = Color(0xFF2C2C2C)

// Text Colors (Dark Theme)
val OnPrimary = Color(0xFF000000)
val OnSecondary = Color(0xFFFFFFFF)
val OnBackground = Color(0xFFFFFFFF)
val OnSurface = Color(0xFFFFFFFF)
val OnSurfaceVariant = Color(0xFFBDBDBD)

// Status Colors
val Success = Color(0xFF4CAF50)
val Warning = Color(0xFFFF9800)
val Error = Color(0xFFF44336)
val Info = Color(0xFF2196F3)

// Driver Status Colors
val StatusAvailable = Color(0xFF4CAF50)
val StatusBusy = Color(0xFFFF9800)
val StatusOffline = Color(0xFF9E9E9E)

// Ride Status Colors
val RidePending = Color(0xFFFF9800)
val RideAccepted = Color(0xFF2196F3)
val RideOngoing = Color(0xFF4CAF50)
val RideCompleted = Color(0xFF9E9E9E)
val RideCancelled = Color(0xFFF44336)

// Earnings Colors
val EarningsPositive = Color(0xFF4CAF50)
val EarningsNeutral = Color(0xFFFFC107)
val EarningsBackground = Color(0xFF2C2C2C)

// Map Colors
val PickupMarker = Color(0xFF4CAF50)
val DropMarker = Color(0xFFF44336)
val DriverMarker = Color(0xFFFFC107)
val RouteColor = Color(0xFFFFC107)
