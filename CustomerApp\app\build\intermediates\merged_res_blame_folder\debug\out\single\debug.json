[{"merged": "com.quickride.customer.app-merged_res-81:/xml_backup_rules.xml.flat", "source": "com.quickride.customer.app-main-83:/xml/backup_rules.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/mipmap-mdpi_ic_launcher.xml.flat", "source": "com.quickride.customer.app-main-83:/mipmap-mdpi/ic_launcher.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/mipmap-hdpi_ic_launcher_round.xml.flat", "source": "com.quickride.customer.app-main-83:/mipmap-hdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.quickride.customer.app-merged_res-81:\\drawable_ic_car.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.quickride.customer.app-main-83:\\drawable\\ic_car.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/mipmap-hdpi_ic_launcher.xml.flat", "source": "com.quickride.customer.app-main-83:/mipmap-hdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.quickride.customer.app-merged_res-81:\\drawable_splash_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.quickride.customer.app-main-83:\\drawable\\splash_background.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/mipmap-mdpi_ic_launcher_round.xml.flat", "source": "com.quickride.customer.app-main-83:/mipmap-mdpi/ic_launcher_round.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/mipmap-xxxhdpi_ic_launcher_round.xml.flat", "source": "com.quickride.customer.app-main-83:/mipmap-xxxhdpi/ic_launcher_round.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/mipmap-xxxhdpi_ic_launcher.xml.flat", "source": "com.quickride.customer.app-main-83:/mipmap-xxxhdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.quickride.customer.app-merged_res-81:\\drawable_ic_location.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.quickride.customer.app-main-83:\\drawable\\ic_location.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/xml_data_extraction_rules.xml.flat", "source": "com.quickride.customer.app-main-83:/xml/data_extraction_rules.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/mipmap-xhdpi_ic_launcher_round.xml.flat", "source": "com.quickride.customer.app-main-83:/mipmap-xhdpi/ic_launcher_round.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/mipmap-xhdpi_ic_launcher.xml.flat", "source": "com.quickride.customer.app-main-83:/mipmap-xhdpi/ic_launcher.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/mipmap-xxhdpi_ic_launcher.xml.flat", "source": "com.quickride.customer.app-main-83:/mipmap-xxhdpi/ic_launcher.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/drawable_ic_launcher_foreground.xml.flat", "source": "com.quickride.customer.app-main-83:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.quickride.customer.app-merged_res-81:/mipmap-xxhdpi_ic_launcher_round.xml.flat", "source": "com.quickride.customer.app-main-83:/mipmap-xxhdpi/ic_launcher_round.xml"}]