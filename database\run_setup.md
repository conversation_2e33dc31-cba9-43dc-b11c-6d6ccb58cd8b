# QuickRide Database Setup Instructions

## Prerequisites
1. Create a Supabase account at [supabase.com](https://supabase.com)
2. Create a new project in your Supabase dashboard
3. Note down your project URL and anon key

## Setup Steps

### Step 1: Run Main Schema and Functions
1. Go to your Supabase dashboard
2. Navigate to **SQL Editor** in the left sidebar
3. Copy and paste the entire content of `setup_complete.sql` into the SQL editor
4. Click **Run** to execute the script

### Step 2: Set up Row Level Security (RLS) Policies
1. In the SQL Editor, create a new query
2. Copy and paste the content of `policies.sql`
3. Click **Run** to execute

### Step 3: Configure Realtime Subscriptions
1. In the SQL Editor, create a new query
2. Copy and paste the content of `realtime_config.sql`
3. Click **Run** to execute

### Step 4: Enable Realtime for Tables
1. Go to **Database** → **Replication** in your Supabase dashboard
2. Enable realtime for the following tables:
   - `users`
   - `drivers`
   - `ride_requests`
   - `ride_history`
   - `driver_locations`
   - `notifications`

### Step 5: Update Android App Configuration
1. Open `CustomerApp/app/src/main/java/com/quickride/customer/utils/Constants.kt`
2. Replace the placeholder values with your actual Supabase project details:
   ```kotlin
   const val SUPABASE_URL = "YOUR_SUPABASE_PROJECT_URL"
   const val SUPABASE_ANON_KEY = "YOUR_SUPABASE_ANON_KEY"
   ```
3. Do the same for `DriverApp/app/src/main/java/com/quickride/driver/utils/Constants.kt`

### Step 6: Test the Setup
1. Go to **Table Editor** in your Supabase dashboard
2. You should see all the created tables:
   - users
   - drivers
   - ride_requests
   - ride_history
   - driver_locations
   - notifications

### Step 7: Insert Test Data (Optional)
You can insert some test data to verify everything is working:

```sql
-- Insert a test customer
INSERT INTO auth.users (id, email) VALUES ('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>');
INSERT INTO public.users (id, name, phone, user_type) VALUES 
('550e8400-e29b-41d4-a716-446655440000', 'Test Customer', '+919876543210', 'customer');

-- Insert a test driver
INSERT INTO auth.users (id, email) VALUES ('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>');
INSERT INTO public.users (id, name, phone, user_type) VALUES 
('550e8400-e29b-41d4-a716-446655440001', 'Test Driver', '+919876543211', 'driver');

INSERT INTO public.drivers (id, name, vehicle_no, vehicle_model, license_number, status, is_verified) VALUES 
('550e8400-e29b-41d4-a716-446655440001', 'Test Driver', 'KA01AB1234', 'Maruti Swift', 'DL1234567890', 'available', true);
```

## Troubleshooting

### Common Issues:
1. **Extension not found**: Make sure PostGIS extension is enabled in your Supabase project
2. **Permission denied**: Ensure you're running the scripts as the project owner
3. **Type already exists**: This is normal if you're re-running the script

### Verification:
- Check that all tables are created in the Table Editor
- Verify that RLS policies are applied (you should see policy icons next to table names)
- Test realtime functionality in the API docs

## Next Steps
After successful database setup:
1. Configure Google Maps API keys in the Android apps
2. Set up Razorpay payment gateway credentials
3. Build and test the Android applications
4. Deploy to production when ready

## Support
If you encounter any issues:
1. Check the Supabase logs in your dashboard
2. Verify all environment variables are correctly set
3. Ensure your Supabase project has sufficient resources
