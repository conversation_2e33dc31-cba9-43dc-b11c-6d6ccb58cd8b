package com.quickride.driver.data.model

import kotlinx.serialization.Serializable

@Serializable
data class RideRequest(
    val id: String,
    val customerId: String,
    val customerName: String,
    val customerPhone: String,
    val driverId: String? = null,
    val pickupLocation: Location,
    val pickupAddress: String,
    val dropLocation: Location,
    val dropAddress: String,
    val estimatedFare: Double,
    val actualFare: Double? = null,
    val distanceKm: Double? = null,
    val estimatedDurationMinutes: Int? = null,
    val status: RideStatus = RideStatus.PENDING,
    val paymentMode: PaymentMode? = null,
    val specialInstructions: String? = null,
    val createdAt: String,
    val acceptedAt: String? = null,
    val startedAt: String? = null,
    val completedAt: String? = null,
    val cancelledAt: String? = null,
    val updatedAt: String
)

@Serializable
data class RideHistory(
    val id: String,
    val rideId: String,
    val customerId: String,
    val customerName: String,
    val driverId: String,
    val pickupAddress: String,
    val dropAddress: String,
    val customerRating: Int? = null,
    val driverRating: Int? = null,
    val customerFeedback: String? = null,
    val driverFeedback: String? = null,
    val paymentMode: PaymentMode,
    val finalFare: Double,
    val tipAmount: Double = 0.0,
    val distanceKm: Double? = null,
    val durationMinutes: Int? = null,
    val createdAt: String
)

@Serializable
data class TripSummary(
    val rideId: String,
    val customerName: String,
    val pickupAddress: String,
    val dropAddress: String,
    val distanceKm: Double,
    val durationMinutes: Int,
    val fare: Double,
    val paymentMode: PaymentMode,
    val completedAt: String
)

enum class RideStatus {
    PENDING, ACCEPTED, ONGOING, COMPLETED, CANCELLED
}

enum class PaymentMode {
    CASH, UPI, CARD, WALLET
}
