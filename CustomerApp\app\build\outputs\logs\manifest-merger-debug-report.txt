-- Merging decision tree log ---
meta-data#com.google.android.geo.API_KEY
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:44:9-46:47
	android:value
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:46:13-44
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:45:13-58
manifest
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:2:1-123:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:2:1-123:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:2:1-123:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:2:1-123:12
MERGED from [com.google.maps.android:maps-compose:4.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cb2b09e512b6810578d436f072c3ca1\transformed\maps-compose-4.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.maps.android:maps-ktx:5.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52836353ffb797b3e0527b050e9cc99\transformed\maps-ktx-5.0.0\AndroidManifest.xml:18:1-23:12
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eafcd5c699b0fb51be49a000d12d073e\transformed\viewbinding-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff79107374b2801fc3c3e77358c59b9b\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6a55e542f66fab3efe3046048aea03a\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\87a89ed464e5a2dafff2782b23796a96\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d6e12542967d03c6a3c80159d0b8dd6\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\376dc31ccb5d2891347733d2b12f9b97\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\77318c4cb9aaae0ed79a0f367466b9cb\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\66f9dfc523c61d41ce90c6af3ce11dff\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1d30154181cf23c0bd663117e40b046\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\83e5e2b178069d0cc8cbffee7b842817\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a207269cd93e115977cde60dd2975130\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9d944808d82dee8a05be75e2215e8cb\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5a0c0f0471b492a7e8aac6ad81aa6cb\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb035fc21c535ab29dc2e5bb4c09fb1c\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ff92608449b3e8dd28d18f424b3d289\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f71b4cd6ec6de95d619404b6789719b8\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\56ea0d131bc0f786175dbd0b5daaeb8b\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e85b7c3ad0df798520dc2031c12bb41\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:2:1-66:12
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dbe9381c9d479d3e9bf94a1e5011545\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a768e469361dfe4da94c996c751ef929\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f58916d4e2a4fbde3703e9ff94b68e34\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\98298dfe8ad86f25496535c5badcc3e4\transformed\play-services-auth-api-phone-17.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7349066c52db75ebcb4e3051a78bf7c3\transformed\play-services-auth-base-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00a1718facb5b24b443c69ae42a93d5a\transformed\play-services-identity-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d4ef911b1873266cc18403946b7b716\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fb5231e2a8af35ec424ca8b34f6a3cf\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc4887c96fa4fff2ca15ef34a047b3dd\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d654605e44d757f14a818029a7dd7ad\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8d4be1a1c7480e23037799cb5e0b535\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6f02c0763808af7366b11b1f44fc71b\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e6dc7d2cd1578906716f1acc887156f\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\423e6e84659f1b6734f4680512cc91b8\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c15669c5468af7f76117e196a8f9820\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1322accd082fcc047344193ecb800a6\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce4e7211131352808683c98425301653\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b82a62117ce898a798754b2de4702241\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a7df0f92ff33183244383dbcbaff3f8\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2343640816de1c844ff258ff51f18ce\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\94c1afaece45923cf79b01f66cedee9e\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\91de0bfce744676ce10405de9dc79587\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0cd80aa0e16d7e5aa4ac6c2b50d1f68f\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3e0b80e1b913167958cfb5a16891780\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9605bae7c7cc0d064468cd629fdc34ad\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\68c2c807da31bf6c9eecfaba3171f06a\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4479790c2aadc9d9c4acfa86c8b365cf\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1809519306d89cb586f3c3e7951acbb\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\085865c6c9051613e6535651fb55e5ea\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1fc820fa3d45d0ea4ba5a9c382de085\transformed\postgrest-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9219de28d4fba30aa5bdf2ab48cd094\transformed\realtime-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:2:1-20:12
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\108922f6596a1e3c77c8190d4dae5482\transformed\supabase-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c9c64bbbc3951e9c50194166876a1676\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\748731d1c3882111b5057fc8aa08cf02\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97b498bf2a437d694674e540eb7054bd\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f171036b797f2ea431c9a25ee808ba9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94ddbe37d9bac14906d921547fb8e643\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d92a87d8eac6a67b0e0d74c79ed357f2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e545a76caff6bbcc89f31d44951677a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f999b69e8cf55981a4849d09536d4bff\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c2809cdc828dfd2206bf748b0deef15\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1dcb9e7f65bc4362fb02b0aa430769be\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f639bba8963ebad11510e21bafc83c90\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\612860d30c0ab7980ecbcf2492941a10\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\184730f99e24650e27fc6cb22e840c76\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5fcc84de26bbee5256e2603733f8efe\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b90347aefdb8277a15a649c7d5249c4\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99665605b595b1152410508ab954809\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\24afabbc4e11b82eb022302e0c0f1130\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\632cab5fc564a5b123fbe45f4af0d028\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\64f1b4556e4dc941effdc20e883bcdae\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\11dc6d9d4570eee59edeec3ba3de496d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bb36d958649bb6aef42ec3d35da08d10\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f18e94e2501e9b2475e2985ca9adff1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c0d3d2d72b0bc83e12c76c6a63f5f89\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e41fac7a0d4201f83940ce931bdcbd4\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\405d6091cfea6ea77c8c134b766b7aad\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df15c358564070153a7f42d7060fd8e8\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f7c442f9f9f43b1a617e6ca6fe146292\transformed\browser-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de799a60970e378b32dec27dcd1d05c8\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c1379fbb72de1dc147d1538d0def529\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d726d248d5ea6708eee121df06aa7d0f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\858b3ffc1e403369eb67d5731fc29840\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\b533a57150a8da2c626213b8aec2b5f1\transformed\krypto-debug\AndroidManifest.xml:2:1-9:12
MERGED from [co.touchlab:kermit-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\97ef2c9408dbef454dffbe72fdd33fda\transformed\kermit-debug\AndroidManifest.xml:2:1-7:12
MERGED from [co.touchlab:kermit-core-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cfe86f1fda21f990e41b8d89fa0f56e\transformed\kermit-core-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1009c63ae3a2992e27bc32fbb6d45b86\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7716b2d4939c62574bd5a82e67abe9e\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73a1a37341e337501e39ffce655b28c6\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9557fc0008187980398f2723724b8dbb\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:17:1-35:12
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3810a0ba1e56316e82c8b2f7c894344\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a0f81448654268c1d06dc75516ed2b4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6582981cb234fc97b7efbd5a5a8e89e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afc8c8d978ec8d34a3769c4f54b2ea1f\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\971e97cde0947986cc6ad8fc5c1b7dd6\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8618f1cab7662103e241e9f5a69b812\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8c11c36a65f71587f88fac582a62408\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\15f0e600f37bf9bb786bd6fd5e09e987\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfbaa4fbef844f4d7e23df432796bb7a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00d805a01870c328f4262c7228b6a2d9\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\050970fde974772bd7d90a7c92b6a722\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\19c296ba920094a1e2dfbb29ad08141f\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f1d8d46984663877b4582486d2cebb2\transformed\multiplatform-settings-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8c3a0d5a7d79d623ec72a4f3aa8b223\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:10:5-79
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:11:5-81
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:12:5-85
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:12:22-82
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:15:5-75
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:15:22-72
uses-permission#android.permission.READ_SMS
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.RECEIVE_SMS
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:17:5-70
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:17:22-67
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:20:5-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:20:22-74
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:21:5-66
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:21:22-63
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:24:5-65
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:24:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:27:5-80
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:27:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:28:5-81
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:28:22-78
application
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:30:5-121:19
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:30:5-121:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a207269cd93e115977cde60dd2975130\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a207269cd93e115977cde60dd2975130\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\56ea0d131bc0f786175dbd0b5daaeb8b\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\56ea0d131bc0f786175dbd0b5daaeb8b\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:41:5-64:19
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:41:5-64:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f58916d4e2a4fbde3703e9ff94b68e34\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f58916d4e2a4fbde3703e9ff94b68e34\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:22:5-37:19
MERGED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:22:5-37:19
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00a1718facb5b24b443c69ae42a93d5a\transformed\play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00a1718facb5b24b443c69ae42a93d5a\transformed\play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d4ef911b1873266cc18403946b7b716\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d4ef911b1873266cc18403946b7b716\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a0f81448654268c1d06dc75516ed2b4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a0f81448654268c1d06dc75516ed2b4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8c11c36a65f71587f88fac582a62408\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8c11c36a65f71587f88fac582a62408\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:38:9-35
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:36:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:34:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:37:9-50
	tools:targetApi
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:41:9-29
	android:icon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:35:9-45
	android:allowBackup
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:32:9-35
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:39:9-55
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:33:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:40:9-44
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:31:9-45
activity#com.quickride.customer.MainActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:49:9-58:20
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:52:13-45
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:51:13-36
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:53:13-59
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:50:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:54:13-57:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:17-69
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:56:17-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:56:27-74
activity#com.quickride.customer.ui.auth.AuthActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:61:9-64:74
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:63:13-37
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:64:13-71
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:62:13-49
activity#com.quickride.customer.ui.splash.SplashActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:67:9-75:20
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:69:13-36
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:70:13-66
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:68:13-53
activity#com.quickride.customer.ui.ride.RideActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:78:9-81:74
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:80:13-37
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:81:13-71
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:79:13-49
activity#com.quickride.customer.ui.profile.ProfileActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:84:9-86:40
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:86:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:85:13-55
activity#com.quickride.customer.ui.history.HistoryActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:89:9-91:40
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:91:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:90:13-55
activity#com.quickride.customer.ui.payment.PaymentActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:94:9-96:40
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:96:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:95:13-55
service#com.quickride.customer.service.LocationService
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:99:9-103:56
	android:enabled
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:101:13-35
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:102:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:103:13-53
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:100:13-52
service#com.quickride.customer.service.NotificationService
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:106:9-112:19
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:108:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:107:13-56
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:109:13-111:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:110:17-78
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:110:25-75
activity#com.razorpay.CheckoutActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:115:9-119:48
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:51:9-59:20
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:51:9-59:20
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:117:13-37
		REJECTED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:54:13-36
	android:configChanges
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:53:13-83
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:118:13-49
	tools:replace
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:119:13-45
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:116:13-57
uses-sdk
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
MERGED from [com.google.maps.android:maps-compose:4.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cb2b09e512b6810578d436f072c3ca1\transformed\maps-compose-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:maps-compose:4.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cb2b09e512b6810578d436f072c3ca1\transformed\maps-compose-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:maps-ktx:5.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52836353ffb797b3e0527b050e9cc99\transformed\maps-ktx-5.0.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.maps.android:maps-ktx:5.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52836353ffb797b3e0527b050e9cc99\transformed\maps-ktx-5.0.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eafcd5c699b0fb51be49a000d12d073e\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eafcd5c699b0fb51be49a000d12d073e\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff79107374b2801fc3c3e77358c59b9b\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff79107374b2801fc3c3e77358c59b9b\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6a55e542f66fab3efe3046048aea03a\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6a55e542f66fab3efe3046048aea03a\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\87a89ed464e5a2dafff2782b23796a96\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\87a89ed464e5a2dafff2782b23796a96\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d6e12542967d03c6a3c80159d0b8dd6\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d6e12542967d03c6a3c80159d0b8dd6\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\376dc31ccb5d2891347733d2b12f9b97\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\376dc31ccb5d2891347733d2b12f9b97\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\77318c4cb9aaae0ed79a0f367466b9cb\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\77318c4cb9aaae0ed79a0f367466b9cb\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\66f9dfc523c61d41ce90c6af3ce11dff\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\66f9dfc523c61d41ce90c6af3ce11dff\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1d30154181cf23c0bd663117e40b046\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1d30154181cf23c0bd663117e40b046\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\83e5e2b178069d0cc8cbffee7b842817\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\83e5e2b178069d0cc8cbffee7b842817\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a207269cd93e115977cde60dd2975130\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a207269cd93e115977cde60dd2975130\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9d944808d82dee8a05be75e2215e8cb\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9d944808d82dee8a05be75e2215e8cb\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5a0c0f0471b492a7e8aac6ad81aa6cb\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5a0c0f0471b492a7e8aac6ad81aa6cb\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb035fc21c535ab29dc2e5bb4c09fb1c\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb035fc21c535ab29dc2e5bb4c09fb1c\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ff92608449b3e8dd28d18f424b3d289\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ff92608449b3e8dd28d18f424b3d289\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f71b4cd6ec6de95d619404b6789719b8\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f71b4cd6ec6de95d619404b6789719b8\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\56ea0d131bc0f786175dbd0b5daaeb8b\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\56ea0d131bc0f786175dbd0b5daaeb8b\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e85b7c3ad0df798520dc2031c12bb41\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e85b7c3ad0df798520dc2031c12bb41\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:6:5-44
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dbe9381c9d479d3e9bf94a1e5011545\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dbe9381c9d479d3e9bf94a1e5011545\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a768e469361dfe4da94c996c751ef929\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a768e469361dfe4da94c996c751ef929\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f58916d4e2a4fbde3703e9ff94b68e34\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f58916d4e2a4fbde3703e9ff94b68e34\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\98298dfe8ad86f25496535c5badcc3e4\transformed\play-services-auth-api-phone-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\98298dfe8ad86f25496535c5badcc3e4\transformed\play-services-auth-api-phone-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7349066c52db75ebcb4e3051a78bf7c3\transformed\play-services-auth-base-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7349066c52db75ebcb4e3051a78bf7c3\transformed\play-services-auth-base-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00a1718facb5b24b443c69ae42a93d5a\transformed\play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00a1718facb5b24b443c69ae42a93d5a\transformed\play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d4ef911b1873266cc18403946b7b716\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d4ef911b1873266cc18403946b7b716\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fb5231e2a8af35ec424ca8b34f6a3cf\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fb5231e2a8af35ec424ca8b34f6a3cf\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc4887c96fa4fff2ca15ef34a047b3dd\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc4887c96fa4fff2ca15ef34a047b3dd\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d654605e44d757f14a818029a7dd7ad\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d654605e44d757f14a818029a7dd7ad\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8d4be1a1c7480e23037799cb5e0b535\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8d4be1a1c7480e23037799cb5e0b535\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6f02c0763808af7366b11b1f44fc71b\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6f02c0763808af7366b11b1f44fc71b\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e6dc7d2cd1578906716f1acc887156f\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e6dc7d2cd1578906716f1acc887156f\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\423e6e84659f1b6734f4680512cc91b8\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\423e6e84659f1b6734f4680512cc91b8\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c15669c5468af7f76117e196a8f9820\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c15669c5468af7f76117e196a8f9820\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1322accd082fcc047344193ecb800a6\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1322accd082fcc047344193ecb800a6\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce4e7211131352808683c98425301653\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce4e7211131352808683c98425301653\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b82a62117ce898a798754b2de4702241\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b82a62117ce898a798754b2de4702241\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a7df0f92ff33183244383dbcbaff3f8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a7df0f92ff33183244383dbcbaff3f8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2343640816de1c844ff258ff51f18ce\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2343640816de1c844ff258ff51f18ce\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\94c1afaece45923cf79b01f66cedee9e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\94c1afaece45923cf79b01f66cedee9e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\91de0bfce744676ce10405de9dc79587\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\91de0bfce744676ce10405de9dc79587\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0cd80aa0e16d7e5aa4ac6c2b50d1f68f\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0cd80aa0e16d7e5aa4ac6c2b50d1f68f\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3e0b80e1b913167958cfb5a16891780\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3e0b80e1b913167958cfb5a16891780\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9605bae7c7cc0d064468cd629fdc34ad\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9605bae7c7cc0d064468cd629fdc34ad\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\68c2c807da31bf6c9eecfaba3171f06a\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\68c2c807da31bf6c9eecfaba3171f06a\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4479790c2aadc9d9c4acfa86c8b365cf\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4479790c2aadc9d9c4acfa86c8b365cf\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1809519306d89cb586f3c3e7951acbb\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1809519306d89cb586f3c3e7951acbb\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\085865c6c9051613e6535651fb55e5ea\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\085865c6c9051613e6535651fb55e5ea\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1fc820fa3d45d0ea4ba5a9c382de085\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1fc820fa3d45d0ea4ba5a9c382de085\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9219de28d4fba30aa5bdf2ab48cd094\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9219de28d4fba30aa5bdf2ab48cd094\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\108922f6596a1e3c77c8190d4dae5482\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\108922f6596a1e3c77c8190d4dae5482\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c9c64bbbc3951e9c50194166876a1676\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c9c64bbbc3951e9c50194166876a1676\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\748731d1c3882111b5057fc8aa08cf02\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\748731d1c3882111b5057fc8aa08cf02\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97b498bf2a437d694674e540eb7054bd\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97b498bf2a437d694674e540eb7054bd\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f171036b797f2ea431c9a25ee808ba9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f171036b797f2ea431c9a25ee808ba9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94ddbe37d9bac14906d921547fb8e643\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94ddbe37d9bac14906d921547fb8e643\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d92a87d8eac6a67b0e0d74c79ed357f2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d92a87d8eac6a67b0e0d74c79ed357f2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e545a76caff6bbcc89f31d44951677a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e545a76caff6bbcc89f31d44951677a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f999b69e8cf55981a4849d09536d4bff\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f999b69e8cf55981a4849d09536d4bff\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c2809cdc828dfd2206bf748b0deef15\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c2809cdc828dfd2206bf748b0deef15\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1dcb9e7f65bc4362fb02b0aa430769be\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1dcb9e7f65bc4362fb02b0aa430769be\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f639bba8963ebad11510e21bafc83c90\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f639bba8963ebad11510e21bafc83c90\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\612860d30c0ab7980ecbcf2492941a10\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\612860d30c0ab7980ecbcf2492941a10\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\184730f99e24650e27fc6cb22e840c76\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\184730f99e24650e27fc6cb22e840c76\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5fcc84de26bbee5256e2603733f8efe\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5fcc84de26bbee5256e2603733f8efe\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b90347aefdb8277a15a649c7d5249c4\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b90347aefdb8277a15a649c7d5249c4\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99665605b595b1152410508ab954809\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99665605b595b1152410508ab954809\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\24afabbc4e11b82eb022302e0c0f1130\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\24afabbc4e11b82eb022302e0c0f1130\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\632cab5fc564a5b123fbe45f4af0d028\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\632cab5fc564a5b123fbe45f4af0d028\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\64f1b4556e4dc941effdc20e883bcdae\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\64f1b4556e4dc941effdc20e883bcdae\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\11dc6d9d4570eee59edeec3ba3de496d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\11dc6d9d4570eee59edeec3ba3de496d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bb36d958649bb6aef42ec3d35da08d10\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bb36d958649bb6aef42ec3d35da08d10\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f18e94e2501e9b2475e2985ca9adff1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f18e94e2501e9b2475e2985ca9adff1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c0d3d2d72b0bc83e12c76c6a63f5f89\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c0d3d2d72b0bc83e12c76c6a63f5f89\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e41fac7a0d4201f83940ce931bdcbd4\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e41fac7a0d4201f83940ce931bdcbd4\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\405d6091cfea6ea77c8c134b766b7aad\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\405d6091cfea6ea77c8c134b766b7aad\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df15c358564070153a7f42d7060fd8e8\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df15c358564070153a7f42d7060fd8e8\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f7c442f9f9f43b1a617e6ca6fe146292\transformed\browser-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f7c442f9f9f43b1a617e6ca6fe146292\transformed\browser-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de799a60970e378b32dec27dcd1d05c8\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de799a60970e378b32dec27dcd1d05c8\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c1379fbb72de1dc147d1538d0def529\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c1379fbb72de1dc147d1538d0def529\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d726d248d5ea6708eee121df06aa7d0f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d726d248d5ea6708eee121df06aa7d0f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\858b3ffc1e403369eb67d5731fc29840\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\858b3ffc1e403369eb67d5731fc29840\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\b533a57150a8da2c626213b8aec2b5f1\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\b533a57150a8da2c626213b8aec2b5f1\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [co.touchlab:kermit-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\97ef2c9408dbef454dffbe72fdd33fda\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\97ef2c9408dbef454dffbe72fdd33fda\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cfe86f1fda21f990e41b8d89fa0f56e\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cfe86f1fda21f990e41b8d89fa0f56e\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1009c63ae3a2992e27bc32fbb6d45b86\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1009c63ae3a2992e27bc32fbb6d45b86\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7716b2d4939c62574bd5a82e67abe9e\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7716b2d4939c62574bd5a82e67abe9e\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73a1a37341e337501e39ffce655b28c6\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73a1a37341e337501e39ffce655b28c6\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9557fc0008187980398f2723724b8dbb\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9557fc0008187980398f2723724b8dbb\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3810a0ba1e56316e82c8b2f7c894344\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3810a0ba1e56316e82c8b2f7c894344\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a0f81448654268c1d06dc75516ed2b4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a0f81448654268c1d06dc75516ed2b4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6582981cb234fc97b7efbd5a5a8e89e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6582981cb234fc97b7efbd5a5a8e89e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afc8c8d978ec8d34a3769c4f54b2ea1f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afc8c8d978ec8d34a3769c4f54b2ea1f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\971e97cde0947986cc6ad8fc5c1b7dd6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\971e97cde0947986cc6ad8fc5c1b7dd6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8618f1cab7662103e241e9f5a69b812\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8618f1cab7662103e241e9f5a69b812\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8c11c36a65f71587f88fac582a62408\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8c11c36a65f71587f88fac582a62408\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\15f0e600f37bf9bb786bd6fd5e09e987\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\15f0e600f37bf9bb786bd6fd5e09e987\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfbaa4fbef844f4d7e23df432796bb7a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfbaa4fbef844f4d7e23df432796bb7a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00d805a01870c328f4262c7228b6a2d9\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00d805a01870c328f4262c7228b6a2d9\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\050970fde974772bd7d90a7c92b6a722\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\050970fde974772bd7d90a7c92b6a722\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\19c296ba920094a1e2dfbb29ad08141f\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\19c296ba920094a1e2dfbb29ad08141f\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f1d8d46984663877b4582486d2cebb2\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f1d8d46984663877b4582486d2cebb2\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8c3a0d5a7d79d623ec72a4f3aa8b223\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8c3a0d5a7d79d623ec72a4f3aa8b223\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
queries
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:10:5-39:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.intent.action.VIEW+data:mimeType:*/*+data:scheme:*
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:11:9-17:18
action#android.intent.action.VIEW
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:12:13-65
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:12:21-62
data
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:14:13-16:38
	android:scheme
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:16:17-35
	android:mimeType
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:15:17-39
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:host:pay+data:mimeType:*/*+data:scheme:upi
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:18:9-27:18
category#android.intent.category.BROWSABLE
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:21:13-74
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:21:23-71
intent#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:28:9-30:18
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:31:9-35:18
action#android.intent.action.SEND
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:32:13-65
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:32:21-62
intent#action:name:rzp.device_token.share
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:36:9-38:18
action#rzp.device_token.share
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:37:13-61
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:37:21-58
receiver#com.razorpay.RzpTokenReceiver
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:42:9-49:20
	android:exported
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:44:13-36
	tools:ignore
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:45:13-44
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:43:13-57
intent-filter#action:name:rzp.device_token.share
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:46:13-48:29
intent-filter#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:56:13-58:29
meta-data#com.razorpay.plugin.googlepay_all
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:61:9-63:58
	android:value
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:63:13-55
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc901eee192cab640ea3635524a03b5\transformed\checkout-1.6.33\AndroidManifest.xml:62:13-61
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7455d67badf651ecc33203128856f490\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:33:9-36:110
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:35:13-36
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba161917268db02864c9ab4b00d07cc4\transformed\play-services-auth-17.0.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\471cdef36052226776d107dcedf318a4\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a80351878203576e14d29104a2365afc\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ac4ac12c953d6e25b3c42744b5ca061\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\537e9c0781a1cfa8372b70ed974ef2c4\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8c11c36a65f71587f88fac582a62408\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8c11c36a65f71587f88fac582a62408\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7aa87ca8554f529cd7c1308cb3ae6e4\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#io.github.jan.supabase.gotrue.SupabaseInitializer
ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
	android:value
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
	android:name
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3413813b5fd16d93387ef4da19933423\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7847abf7ee13f352b4983b1edd0e57\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\012611f143e1a359f36907ef66939d83\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.quickride.customer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.quickride.customer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1347aa300b36a7cab68a2bd9530e812a\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#com.russhwolf.settings.SettingsInitializer
ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcc0c5709e5c94e237b7800b16ba6e9\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fe50af59d1d24c5805d4a5aff8e25a1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
