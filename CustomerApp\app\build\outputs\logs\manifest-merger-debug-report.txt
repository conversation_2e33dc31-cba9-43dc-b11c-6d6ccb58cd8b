-- Merging decision tree log ---
meta-data#com.google.android.geo.API_KEY
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:44:9-46:47
	android:value
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:46:13-44
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:45:13-58
manifest
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:2:1-123:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:2:1-123:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:2:1-123:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:2:1-123:12
MERGED from [com.google.maps.android:maps-compose:4.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\722b3836397aee00c4d6061210e500c0\transformed\maps-compose-4.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.maps.android:maps-ktx:5.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\91f1f4b5bd99236b6d3200d7a8fc8001\transformed\maps-ktx-5.0.0\AndroidManifest.xml:18:1-23:12
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e8bc8221d9d0b372f043f60b7110804\transformed\viewbinding-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d3c45045c3a0490226879d8aef5763\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4f0e569d5a8245cc21be53c352ef4c9\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\16719f15778a66b4279b78162d10e3c5\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\6daae57ccc46fb47b176bc1265bdf3a4\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\63d26fbfe4b015beb549bf7c71e41a45\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\80e06c6b66493a8a201765a34d17fc6b\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d3ea5b7d3f56b5fc8f309cfca0186df\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdaf5703801e2dd383b272862325612b\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ee8c82b6bb0c8c2498facab534e42ad\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c33e4c704d078c06a31deec8c6bbf5a\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa748d4bb156f3caf61873492a5b1b98\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe1a530a34a585961db8e2456a941bf\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3710770bebeda87044842fdd26bc9fd\transformed\postgrest-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a8c814da181b6dcf3d194dec8139373\transformed\realtime-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:2:1-20:12
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8ef787bc16905b4f8ad900188dd026df\transformed\supabase-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe298bc52bc2084568625d4ed5b1c8e\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1de40567a4602b351adcbe22b8502b37\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dcf8fb63061ed3cf486a6c68955e5ff6\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1006761e1297f3dd6f40b631040d83cc\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed6ff402ae3d381726621c46c2bbc6c0\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a38c740f714c73665bde6d5b33bf438\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:2:1-66:12
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2cfb53aefdccb4be79d4c4359b272f2\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd824dfbdc200a672547c14a8f408363\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e5f31680feddbe70acfce940deee4e\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b1411df4361fba17262a3e7ee42e1e4\transformed\play-services-auth-api-phone-17.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\17cb123c73e1f5f17e468ad4bfb5b1db\transformed\play-services-auth-base-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e185e13cdc5eb1f2afb638414fbf0af\transformed\play-services-identity-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d8f5487c8d20d85d38a43defb2ea01\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7224febaf9c38042eab6548d44b564\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c448a021d6cf081194a9a8b36cfbe947\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0ad3648812c7daf4fa7e11fafcc1169c\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf5e01954fa1b7d26131c1a96f012169\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\86667447c7f3dd92ac1b3321380b62a6\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\11ca131c315b9bf40daec9b035cf7e49\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5825f99d5f8a1076762bbcd31652d79a\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52d9a20cf95ed8a91d498ac34c09426\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\82b638680c95e42f916d57052b140cf6\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\191e7c7f01a985cfbb44f7d3abb642f6\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\87bf7387048a2fdbc0c300afa0a10f63\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fbe59585e3b02d6ab8247fd823df44d\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\52b456da0b71c6a7bafe9dda892dd878\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e4f2c43e26399d18432ef81e3fdef54\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f25f12cc5a46d384f2032135ef5db4a9\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d6a679ee47d91909e49b2052e46d9c1\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e0522009019ea7cba3b34fddc136aee\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\63c0014755c4d5e1a0115bc7d9cb6669\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\951a2762e90d4e37e89913e537640215\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b85f4371e41d12539fbc3e9701cb45b\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7342f3406eff1398a29ad783ecb6bc2\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f496dd32eef577800453be43a0eaedd\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\428251ece4587a018118f99cbe892605\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7102ff9c47dcb07f761f407ecb01209\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4aa607ad6046fe53944dee969a6f554c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b630370af69469982235e43d5a2b2e8e\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5deb3828fcfe6f23d70ee9b8b93dac6c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\53c7683999742d9a6a0d40f667367374\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40c5c44733879ea3b8a936473734edf\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33b4b1b554c1785840aa02981f39a875\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\130d485c2b1b3afc6231a04deee8b03a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c017d4d4b544ab52e8107679a6a54e50\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0cc830e7edaccafe839168258981b7a2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec6b45fe06d7cda7f28c1333b8e3cbfd\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27b001dd86f9c8714f3931e533a692b6\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9e546f78b588b8a103b2fa73c0f00d6\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2cf358b77d6ffb0cbde742f0aaaad1b7\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a9651cd175c6504c2eae0a50c01cddbe\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\673d657c25b628f79f105d0733295d49\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e3087470bd21259575da6dd4a824d62\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10ce04d7ffca5d2ee810999ecc9e21f6\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee31554eea207ebc8a9edcfcb545012b\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6a24715bf3ee2063480c8f9dfec0f9f\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\104d14484e7af0cfdc9eeb711f2ed685\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6675efe932eccfc576b87996510fa478\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb17092b94e4372fc4a7e4a5b351c502\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cbaf3f6f4c78dc07e694de8f80510d0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a69a118396e4fbadb968f00b1808e70e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f0fb36d5baca3a6217e3aa40765d9fda\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\da67138dd3f2b16a72df15f4b9158c69\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b4fa51a9098a654e2a249ebf6449caa\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba2ad62d4937fe8ebce7c4dcee7d2d37\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b8646194409e22fc598862b29a66f98\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eccd6ece3978e3682bca466c13afaf0a\transformed\browser-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5eef1ca941ae5ba6e603f66e2378d97b\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8c2fb5e94d47c221c573ecafe023780\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\689c9c0b618f7465617987deab653d5c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e86e0d1f117a34f68ae560e51c00eb66\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffe1acb709e7401cf1fd0de0e8c72e27\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8c7ea8aae57aeab980a60980d7de1d5\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e57f5fd0b6892ee03cad9a7ee0f4c35\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\17dd68d710a2654a5d1f2b7aff614e05\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d55865124b0b4fac7750be0ffe2cfaea\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:17:1-35:12
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab02c1f4c42ea0479ec571c7c72c1d87\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4c897cd9083293ff3bc6e12504371191\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8b10d288eb46736e46057d889f9adad\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea084a246d51d903e01ebedd4df9d7e\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1cc762ba7ffcee77cef170733751406\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d1195fe302e1b5fee8a3df9afcfa55d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e365238ca512ac316afc9857aa030ff\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31bd752d6e4976592a4b314a7304eead\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6f0521c197a5dfed5ee77c6a07f5f9d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\defb165b0eb017e6d19dbc3a75b3d82e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\894e372d8491eb36f27d42c363fe62d3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\596b6c2bac0b2b3cad7b5cb111c93175\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e87148289453603b3992aa6c68a0f064\transformed\multiplatform-settings-debug\AndroidManifest.xml:2:1-7:12
MERGED from [co.touchlab:kermit-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eb091205a0a9b76288a3a6c79be4460\transformed\kermit-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4612ac5efaa94eb02574981aa2d6cc5\transformed\krypto-debug\AndroidManifest.xml:2:1-9:12
MERGED from [co.touchlab:kermit-core-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\92c21c7ff67e64b81b000a862823f300\transformed\kermit-core-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1521dbbb93d6a1d2e662d676689da807\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:10:5-79
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:11:5-81
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:12:5-85
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:12:22-82
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:15:5-75
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:15:22-72
uses-permission#android.permission.READ_SMS
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.RECEIVE_SMS
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:17:5-70
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:17:22-67
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:20:5-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:20:22-74
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:21:5-66
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:21:22-63
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:24:5-65
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:24:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:27:5-80
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:27:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:28:5-81
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:28:22-78
application
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:30:5-121:19
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:30:5-121:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe1a530a34a585961db8e2456a941bf\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe1a530a34a585961db8e2456a941bf\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed6ff402ae3d381726621c46c2bbc6c0\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed6ff402ae3d381726621c46c2bbc6c0\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:41:5-64:19
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:41:5-64:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e5f31680feddbe70acfce940deee4e\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e5f31680feddbe70acfce940deee4e\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:22:5-37:19
MERGED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:22:5-37:19
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e185e13cdc5eb1f2afb638414fbf0af\transformed\play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e185e13cdc5eb1f2afb638414fbf0af\transformed\play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d8f5487c8d20d85d38a43defb2ea01\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d8f5487c8d20d85d38a43defb2ea01\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7224febaf9c38042eab6548d44b564\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7224febaf9c38042eab6548d44b564\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c448a021d6cf081194a9a8b36cfbe947\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c448a021d6cf081194a9a8b36cfbe947\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7342f3406eff1398a29ad783ecb6bc2\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7342f3406eff1398a29ad783ecb6bc2\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f496dd32eef577800453be43a0eaedd\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f496dd32eef577800453be43a0eaedd\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b630370af69469982235e43d5a2b2e8e\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b630370af69469982235e43d5a2b2e8e\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5deb3828fcfe6f23d70ee9b8b93dac6c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5deb3828fcfe6f23d70ee9b8b93dac6c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d55865124b0b4fac7750be0ffe2cfaea\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d55865124b0b4fac7750be0ffe2cfaea\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4c897cd9083293ff3bc6e12504371191\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4c897cd9083293ff3bc6e12504371191\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e365238ca512ac316afc9857aa030ff\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e365238ca512ac316afc9857aa030ff\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:38:9-35
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:36:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:34:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:37:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:41:9-29
	android:icon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:35:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:32:9-35
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:39:9-55
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:33:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:40:9-44
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:31:9-45
activity#com.quickride.customer.MainActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:49:9-58:20
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:52:13-45
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:51:13-36
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:53:13-59
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:50:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:54:13-57:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:17-69
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:55:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:56:17-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:56:27-74
activity#com.quickride.customer.ui.auth.AuthActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:61:9-64:74
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:63:13-37
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:64:13-71
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:62:13-49
activity#com.quickride.customer.ui.splash.SplashActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:67:9-75:20
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:69:13-36
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:70:13-66
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:68:13-53
activity#com.quickride.customer.ui.ride.RideActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:78:9-81:74
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:80:13-37
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:81:13-71
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:79:13-49
activity#com.quickride.customer.ui.profile.ProfileActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:84:9-86:40
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:86:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:85:13-55
activity#com.quickride.customer.ui.history.HistoryActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:89:9-91:40
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:91:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:90:13-55
activity#com.quickride.customer.ui.payment.PaymentActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:94:9-96:40
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:96:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:95:13-55
service#com.quickride.customer.service.LocationService
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:99:9-103:56
	android:enabled
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:101:13-35
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:102:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:103:13-53
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:100:13-52
service#com.quickride.customer.service.NotificationService
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:106:9-112:19
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:108:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:107:13-56
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:109:13-111:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:110:17-78
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:110:25-75
activity#com.razorpay.CheckoutActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:115:9-119:48
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:51:9-59:20
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:51:9-59:20
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:117:13-37
		REJECTED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:54:13-36
	android:configChanges
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:53:13-83
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:118:13-49
	tools:replace
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:119:13-45
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml:116:13-57
uses-sdk
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
MERGED from [com.google.maps.android:maps-compose:4.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\722b3836397aee00c4d6061210e500c0\transformed\maps-compose-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:maps-compose:4.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\722b3836397aee00c4d6061210e500c0\transformed\maps-compose-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:maps-ktx:5.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\91f1f4b5bd99236b6d3200d7a8fc8001\transformed\maps-ktx-5.0.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.maps.android:maps-ktx:5.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\91f1f4b5bd99236b6d3200d7a8fc8001\transformed\maps-ktx-5.0.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e8bc8221d9d0b372f043f60b7110804\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e8bc8221d9d0b372f043f60b7110804\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d3c45045c3a0490226879d8aef5763\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d3c45045c3a0490226879d8aef5763\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4f0e569d5a8245cc21be53c352ef4c9\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4f0e569d5a8245cc21be53c352ef4c9\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\16719f15778a66b4279b78162d10e3c5\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\16719f15778a66b4279b78162d10e3c5\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\6daae57ccc46fb47b176bc1265bdf3a4\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\6daae57ccc46fb47b176bc1265bdf3a4\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\63d26fbfe4b015beb549bf7c71e41a45\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\63d26fbfe4b015beb549bf7c71e41a45\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\80e06c6b66493a8a201765a34d17fc6b\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\80e06c6b66493a8a201765a34d17fc6b\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d3ea5b7d3f56b5fc8f309cfca0186df\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d3ea5b7d3f56b5fc8f309cfca0186df\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdaf5703801e2dd383b272862325612b\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdaf5703801e2dd383b272862325612b\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ee8c82b6bb0c8c2498facab534e42ad\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ee8c82b6bb0c8c2498facab534e42ad\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c33e4c704d078c06a31deec8c6bbf5a\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c33e4c704d078c06a31deec8c6bbf5a\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa748d4bb156f3caf61873492a5b1b98\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa748d4bb156f3caf61873492a5b1b98\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe1a530a34a585961db8e2456a941bf\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe1a530a34a585961db8e2456a941bf\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3710770bebeda87044842fdd26bc9fd\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3710770bebeda87044842fdd26bc9fd\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a8c814da181b6dcf3d194dec8139373\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a8c814da181b6dcf3d194dec8139373\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8ef787bc16905b4f8ad900188dd026df\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8ef787bc16905b4f8ad900188dd026df\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe298bc52bc2084568625d4ed5b1c8e\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe298bc52bc2084568625d4ed5b1c8e\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1de40567a4602b351adcbe22b8502b37\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1de40567a4602b351adcbe22b8502b37\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dcf8fb63061ed3cf486a6c68955e5ff6\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dcf8fb63061ed3cf486a6c68955e5ff6\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1006761e1297f3dd6f40b631040d83cc\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1006761e1297f3dd6f40b631040d83cc\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed6ff402ae3d381726621c46c2bbc6c0\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed6ff402ae3d381726621c46c2bbc6c0\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a38c740f714c73665bde6d5b33bf438\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a38c740f714c73665bde6d5b33bf438\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:6:5-44
MERGED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2cfb53aefdccb4be79d4c4359b272f2\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2cfb53aefdccb4be79d4c4359b272f2\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd824dfbdc200a672547c14a8f408363\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd824dfbdc200a672547c14a8f408363\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e5f31680feddbe70acfce940deee4e\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e5f31680feddbe70acfce940deee4e\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b1411df4361fba17262a3e7ee42e1e4\transformed\play-services-auth-api-phone-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b1411df4361fba17262a3e7ee42e1e4\transformed\play-services-auth-api-phone-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\17cb123c73e1f5f17e468ad4bfb5b1db\transformed\play-services-auth-base-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\17cb123c73e1f5f17e468ad4bfb5b1db\transformed\play-services-auth-base-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e185e13cdc5eb1f2afb638414fbf0af\transformed\play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e185e13cdc5eb1f2afb638414fbf0af\transformed\play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d8f5487c8d20d85d38a43defb2ea01\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d8f5487c8d20d85d38a43defb2ea01\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7224febaf9c38042eab6548d44b564\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7224febaf9c38042eab6548d44b564\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c448a021d6cf081194a9a8b36cfbe947\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c448a021d6cf081194a9a8b36cfbe947\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0ad3648812c7daf4fa7e11fafcc1169c\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0ad3648812c7daf4fa7e11fafcc1169c\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf5e01954fa1b7d26131c1a96f012169\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf5e01954fa1b7d26131c1a96f012169\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\86667447c7f3dd92ac1b3321380b62a6\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\86667447c7f3dd92ac1b3321380b62a6\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\11ca131c315b9bf40daec9b035cf7e49\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\11ca131c315b9bf40daec9b035cf7e49\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5825f99d5f8a1076762bbcd31652d79a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5825f99d5f8a1076762bbcd31652d79a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52d9a20cf95ed8a91d498ac34c09426\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52d9a20cf95ed8a91d498ac34c09426\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\82b638680c95e42f916d57052b140cf6\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\82b638680c95e42f916d57052b140cf6\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\191e7c7f01a985cfbb44f7d3abb642f6\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\191e7c7f01a985cfbb44f7d3abb642f6\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\87bf7387048a2fdbc0c300afa0a10f63\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\87bf7387048a2fdbc0c300afa0a10f63\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fbe59585e3b02d6ab8247fd823df44d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fbe59585e3b02d6ab8247fd823df44d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\52b456da0b71c6a7bafe9dda892dd878\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\52b456da0b71c6a7bafe9dda892dd878\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e4f2c43e26399d18432ef81e3fdef54\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e4f2c43e26399d18432ef81e3fdef54\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f25f12cc5a46d384f2032135ef5db4a9\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f25f12cc5a46d384f2032135ef5db4a9\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d6a679ee47d91909e49b2052e46d9c1\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d6a679ee47d91909e49b2052e46d9c1\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e0522009019ea7cba3b34fddc136aee\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e0522009019ea7cba3b34fddc136aee\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\63c0014755c4d5e1a0115bc7d9cb6669\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\63c0014755c4d5e1a0115bc7d9cb6669\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\951a2762e90d4e37e89913e537640215\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\951a2762e90d4e37e89913e537640215\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b85f4371e41d12539fbc3e9701cb45b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b85f4371e41d12539fbc3e9701cb45b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7342f3406eff1398a29ad783ecb6bc2\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7342f3406eff1398a29ad783ecb6bc2\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f496dd32eef577800453be43a0eaedd\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f496dd32eef577800453be43a0eaedd\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\428251ece4587a018118f99cbe892605\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\428251ece4587a018118f99cbe892605\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7102ff9c47dcb07f761f407ecb01209\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7102ff9c47dcb07f761f407ecb01209\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4aa607ad6046fe53944dee969a6f554c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4aa607ad6046fe53944dee969a6f554c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b630370af69469982235e43d5a2b2e8e\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b630370af69469982235e43d5a2b2e8e\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5deb3828fcfe6f23d70ee9b8b93dac6c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5deb3828fcfe6f23d70ee9b8b93dac6c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\53c7683999742d9a6a0d40f667367374\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\53c7683999742d9a6a0d40f667367374\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40c5c44733879ea3b8a936473734edf\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40c5c44733879ea3b8a936473734edf\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33b4b1b554c1785840aa02981f39a875\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33b4b1b554c1785840aa02981f39a875\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\130d485c2b1b3afc6231a04deee8b03a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\130d485c2b1b3afc6231a04deee8b03a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c017d4d4b544ab52e8107679a6a54e50\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c017d4d4b544ab52e8107679a6a54e50\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0cc830e7edaccafe839168258981b7a2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0cc830e7edaccafe839168258981b7a2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec6b45fe06d7cda7f28c1333b8e3cbfd\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec6b45fe06d7cda7f28c1333b8e3cbfd\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27b001dd86f9c8714f3931e533a692b6\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27b001dd86f9c8714f3931e533a692b6\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9e546f78b588b8a103b2fa73c0f00d6\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9e546f78b588b8a103b2fa73c0f00d6\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2cf358b77d6ffb0cbde742f0aaaad1b7\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2cf358b77d6ffb0cbde742f0aaaad1b7\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a9651cd175c6504c2eae0a50c01cddbe\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a9651cd175c6504c2eae0a50c01cddbe\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\673d657c25b628f79f105d0733295d49\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\673d657c25b628f79f105d0733295d49\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e3087470bd21259575da6dd4a824d62\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e3087470bd21259575da6dd4a824d62\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10ce04d7ffca5d2ee810999ecc9e21f6\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10ce04d7ffca5d2ee810999ecc9e21f6\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee31554eea207ebc8a9edcfcb545012b\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee31554eea207ebc8a9edcfcb545012b\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6a24715bf3ee2063480c8f9dfec0f9f\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6a24715bf3ee2063480c8f9dfec0f9f\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\104d14484e7af0cfdc9eeb711f2ed685\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\104d14484e7af0cfdc9eeb711f2ed685\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6675efe932eccfc576b87996510fa478\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6675efe932eccfc576b87996510fa478\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb17092b94e4372fc4a7e4a5b351c502\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb17092b94e4372fc4a7e4a5b351c502\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cbaf3f6f4c78dc07e694de8f80510d0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cbaf3f6f4c78dc07e694de8f80510d0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a69a118396e4fbadb968f00b1808e70e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a69a118396e4fbadb968f00b1808e70e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f0fb36d5baca3a6217e3aa40765d9fda\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f0fb36d5baca3a6217e3aa40765d9fda\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\da67138dd3f2b16a72df15f4b9158c69\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\da67138dd3f2b16a72df15f4b9158c69\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b4fa51a9098a654e2a249ebf6449caa\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b4fa51a9098a654e2a249ebf6449caa\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba2ad62d4937fe8ebce7c4dcee7d2d37\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba2ad62d4937fe8ebce7c4dcee7d2d37\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b8646194409e22fc598862b29a66f98\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b8646194409e22fc598862b29a66f98\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eccd6ece3978e3682bca466c13afaf0a\transformed\browser-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eccd6ece3978e3682bca466c13afaf0a\transformed\browser-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5eef1ca941ae5ba6e603f66e2378d97b\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5eef1ca941ae5ba6e603f66e2378d97b\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8c2fb5e94d47c221c573ecafe023780\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8c2fb5e94d47c221c573ecafe023780\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\689c9c0b618f7465617987deab653d5c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\689c9c0b618f7465617987deab653d5c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e86e0d1f117a34f68ae560e51c00eb66\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e86e0d1f117a34f68ae560e51c00eb66\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffe1acb709e7401cf1fd0de0e8c72e27\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffe1acb709e7401cf1fd0de0e8c72e27\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8c7ea8aae57aeab980a60980d7de1d5\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8c7ea8aae57aeab980a60980d7de1d5\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e57f5fd0b6892ee03cad9a7ee0f4c35\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e57f5fd0b6892ee03cad9a7ee0f4c35\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\17dd68d710a2654a5d1f2b7aff614e05\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\17dd68d710a2654a5d1f2b7aff614e05\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d55865124b0b4fac7750be0ffe2cfaea\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d55865124b0b4fac7750be0ffe2cfaea\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab02c1f4c42ea0479ec571c7c72c1d87\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab02c1f4c42ea0479ec571c7c72c1d87\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4c897cd9083293ff3bc6e12504371191\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4c897cd9083293ff3bc6e12504371191\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8b10d288eb46736e46057d889f9adad\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8b10d288eb46736e46057d889f9adad\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea084a246d51d903e01ebedd4df9d7e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea084a246d51d903e01ebedd4df9d7e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1cc762ba7ffcee77cef170733751406\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1cc762ba7ffcee77cef170733751406\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d1195fe302e1b5fee8a3df9afcfa55d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d1195fe302e1b5fee8a3df9afcfa55d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e365238ca512ac316afc9857aa030ff\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e365238ca512ac316afc9857aa030ff\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31bd752d6e4976592a4b314a7304eead\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31bd752d6e4976592a4b314a7304eead\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6f0521c197a5dfed5ee77c6a07f5f9d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6f0521c197a5dfed5ee77c6a07f5f9d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\defb165b0eb017e6d19dbc3a75b3d82e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\defb165b0eb017e6d19dbc3a75b3d82e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\894e372d8491eb36f27d42c363fe62d3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\894e372d8491eb36f27d42c363fe62d3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\596b6c2bac0b2b3cad7b5cb111c93175\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\596b6c2bac0b2b3cad7b5cb111c93175\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e87148289453603b3992aa6c68a0f064\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e87148289453603b3992aa6c68a0f064\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eb091205a0a9b76288a3a6c79be4460\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eb091205a0a9b76288a3a6c79be4460\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4612ac5efaa94eb02574981aa2d6cc5\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4612ac5efaa94eb02574981aa2d6cc5\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [co.touchlab:kermit-core-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\92c21c7ff67e64b81b000a862823f300\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\92c21c7ff67e64b81b000a862823f300\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1521dbbb93d6a1d2e662d676689da807\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1521dbbb93d6a1d2e662d676689da807\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\projects\rapid\CustomerApp\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b630370af69469982235e43d5a2b2e8e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b630370af69469982235e43d5a2b2e8e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5deb3828fcfe6f23d70ee9b8b93dac6c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5deb3828fcfe6f23d70ee9b8b93dac6c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d55865124b0b4fac7750be0ffe2cfaea\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d55865124b0b4fac7750be0ffe2cfaea\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e365238ca512ac316afc9857aa030ff\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e365238ca512ac316afc9857aa030ff\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:13:13-31
	android:authorities
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:11:13-68
	android:exported
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:10:13-67
meta-data#io.github.jan.supabase.gotrue.SupabaseInitializer
ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
	android:value
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
	android:name
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\edba8b70d814e9675bbb16f56b66f8d0\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
queries
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:10:5-39:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.intent.action.VIEW+data:mimeType:*/*+data:scheme:*
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:11:9-17:18
action#android.intent.action.VIEW
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:12:13-65
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:12:21-62
data
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:14:13-16:38
	android:scheme
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:16:17-35
	android:mimeType
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:15:17-39
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:host:pay+data:mimeType:*/*+data:scheme:upi
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:18:9-27:18
category#android.intent.category.BROWSABLE
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:21:13-74
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:21:23-71
intent#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:28:9-30:18
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:31:9-35:18
action#android.intent.action.SEND
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:32:13-65
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:32:21-62
intent#action:name:rzp.device_token.share
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:36:9-38:18
action#rzp.device_token.share
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:37:13-61
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:37:21-58
receiver#com.razorpay.RzpTokenReceiver
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:42:9-49:20
	android:exported
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:44:13-36
	tools:ignore
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:45:13-44
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:43:13-57
intent-filter#action:name:rzp.device_token.share
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:46:13-48:29
intent-filter#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:56:13-58:29
meta-data#com.razorpay.plugin.googlepay_all
ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:61:9-63:58
	android:value
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:63:13-55
	android:name
		ADDED from [com.razorpay:checkout:1.6.33] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4e0cdc184250ba988b6c82be0ee39\transformed\checkout-1.6.33\AndroidManifest.xml:62:13-61
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b277af596981c63f920526777bd74e\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:33:9-36:110
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:35:13-36
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51dcbbf12e5cdae9cdc74082136f6ec0\transformed\play-services-auth-17.0.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d8f5487c8d20d85d38a43defb2ea01\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d8f5487c8d20d85d38a43defb2ea01\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d8f5487c8d20d85d38a43defb2ea01\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d8f5487c8d20d85d38a43defb2ea01\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c448a021d6cf081194a9a8b36cfbe947\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c448a021d6cf081194a9a8b36cfbe947\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c448a021d6cf081194a9a8b36cfbe947\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7342f3406eff1398a29ad783ecb6bc2\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7342f3406eff1398a29ad783ecb6bc2\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7342f3406eff1398a29ad783ecb6bc2\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f496dd32eef577800453be43a0eaedd\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f496dd32eef577800453be43a0eaedd\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f496dd32eef577800453be43a0eaedd\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b630370af69469982235e43d5a2b2e8e\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b630370af69469982235e43d5a2b2e8e\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b630370af69469982235e43d5a2b2e8e\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5deb3828fcfe6f23d70ee9b8b93dac6c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5deb3828fcfe6f23d70ee9b8b93dac6c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5deb3828fcfe6f23d70ee9b8b93dac6c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d19b3509d9eb41b17a7c8f1c5ff64a86\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.quickride.customer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.quickride.customer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f38c0f79029b1a7c880933489cb9e7c8\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#com.russhwolf.settings.SettingsInitializer
ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d55865124b0b4fac7750be0ffe2cfaea\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d55865124b0b4fac7750be0ffe2cfaea\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d55865124b0b4fac7750be0ffe2cfaea\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a404f20475b0dadb7c9d0495830ff0c7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
