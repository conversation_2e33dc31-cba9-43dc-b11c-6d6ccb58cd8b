# QuickRide – Dual App Taxi Booking System

## 🧩 Overview
QuickRide is a comprehensive taxi booking system consisting of two Android applications:

- **CustomerApp** – for users to book rides
- **DriverApp** – for drivers to accept and manage rides

Backend powered by Supabase for real-time ride management, authentication, and data storage.

## 📂 Project Structure
```
QuickRide/
├── CustomerApp/           # Customer Android App
│   ├── app/
│   ├── gradle/
│   ├── build.gradle
│   └── settings.gradle
│
├── DriverApp/            # Driver Android App
│   ├── app/
│   ├── gradle/
│   ├── build.gradle
│   └── settings.gradle
│
├── database/             # Supabase SQL scripts
│   ├── schema.sql
│   ├── functions.sql
│   └── policies.sql
│
└── docs/                # Documentation
    ├── api.md
    ├── setup.md
    └── user-guide.md
```

## 🌐 Supabase Configuration
- **Project URL**: https://rrvqtlnzzwmyowbeehqt.supabase.co
- **Anon Key**: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.GqjhlBzRiY-nvG9H2nnH4XuXzZtsIEOMV2lcIxhdBbU

## 💡 Tech Stack
- **Frontend**: Android Studio (Kotlin)
- **Backend**: Supabase
- **Maps**: Google Maps API
- **Location**: FusedLocationProvider
- **Payment**: Razorpay/UPI
- **Realtime**: Supabase Realtime
- **Auth**: Supabase Auth

## 🚀 Quick Start
1. Clone the repository
2. Set up Supabase database using scripts in `/database`
3. Configure Google Maps API keys
4. Open CustomerApp and DriverApp in Android Studio
5. Build and run the applications

## 📱 Features

### Customer App
- ✅ Phone/Email Authentication
- ✅ Google Maps Integration
- ✅ Ride Booking & Tracking
- ✅ Fare Estimation
- ✅ Payment Integration
- ✅ Ride History & Rating

### Driver App
- ✅ Driver Authentication & Vehicle Registration
- ✅ Real-time Location Tracking
- ✅ Ride Request Management
- ✅ Navigation & Trip Management
- ✅ Earnings Dashboard

## 🔄 Ride Flow
1. Customer books ride → Creates ride_request
2. Nearest available driver receives notification
3. Driver accepts → Updates ride_request with driver_id
4. Real-time tracking during trip
5. Trip completion → Payment & rating

## 📄 License
MIT License - see LICENSE file for details
