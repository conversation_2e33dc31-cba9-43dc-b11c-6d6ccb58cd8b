plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.android.libraries.mapsplatform.secrets-gradle-plugin'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    id 'org.jetbrains.kotlin.plugin.serialization'
}

android {
    namespace 'com.quickride.customer'
    compileSdk 33

    defaultConfig {
        applicationId "com.quickride.customer"
        minSdk 24
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }

        // Enable multidex for large apps
        multiDexEnabled true
    }

    buildTypes {
        debug {
            minifyEnabled false
            debuggable true
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
        freeCompilerArgs += [
            "-opt-in=kotlin.RequiresOptIn"
        ]
    }

    buildFeatures {
        compose true
        viewBinding true
        buildConfig true
    }

    composeOptions {
        kotlinCompilerExtensionVersion '1.5.8'
    }

    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
            excludes += '/META-INF/DEPENDENCIES'
            excludes += '/META-INF/LICENSE'
            excludes += '/META-INF/LICENSE.txt'
            excludes += '/META-INF/license.txt'
            excludes += '/META-INF/NOTICE'
            excludes += '/META-INF/NOTICE.txt'
            excludes += '/META-INF/notice.txt'
            excludes += '/META-INF/ASL2.0'
            excludes += '/META-INF/*.kotlin_module'
        }
    }


}

dependencies {
    // Multidex support
    implementation 'androidx.multidex:multidex:2.0.1'

    // Core Android
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'
    implementation 'androidx.appcompat:appcompat:1.6.1'

    // Material Design 3
    implementation 'com.google.android.material:material:1.11.0'

    // Compose BOM - manages all compose library versions
    implementation platform('androidx.compose:compose-bom:2024.02.00')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.material:material-icons-extended'

    // Navigation
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.6'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.6'
    implementation 'androidx.navigation:navigation-compose:2.7.6'

    // ViewModel and LiveData
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    // Google Maps
    implementation 'com.google.android.gms:play-services-maps:18.2.0'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation 'com.google.maps.android:maps-compose:4.3.0'

    // Supabase - using consistent versions
    def supabase_version = "2.0.4"
    implementation "io.github.jan-tennert.supabase:postgrest-kt:$supabase_version"
    implementation "io.github.jan-tennert.supabase:gotrue-kt:$supabase_version"
    implementation "io.github.jan-tennert.supabase:realtime-kt:$supabase_version"

    // Ktor client - consistent versions
    def ktor_version = "2.3.7"
    implementation "io.ktor:ktor-client-android:$ktor_version"
    implementation "io.ktor:ktor-client-core:$ktor_version"
    implementation "io.ktor:ktor-utils:$ktor_version"
    implementation "io.ktor:ktor-client-content-negotiation:$ktor_version"
    implementation "io.ktor:ktor-serialization-kotlinx-json:$ktor_version"

    // Serialization
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.2'

    // Image Loading
    implementation 'io.coil-kt:coil-compose:2.5.0'

    // Permissions
    implementation 'com.google.accompanist:accompanist-permissions:0.32.0'

    // Date/Time
    implementation 'org.jetbrains.kotlinx:kotlinx-datetime:0.5.0'

    // Payment (Razorpay)
    implementation 'com.razorpay:checkout:1.6.33'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation platform('androidx.compose:compose-bom:2024.02.00')
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
}
