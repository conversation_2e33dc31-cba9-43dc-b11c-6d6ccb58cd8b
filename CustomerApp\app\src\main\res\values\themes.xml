<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme -->
    <style name="Theme.QuickRideCustomer" parent="Theme.Material3.DayNight">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/primary_variant</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/background</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- Action bar -->
        <item name="colorPrimaryDark">@color/primary_dark</item>
    </style>

    <!-- Theme without action bar -->
    <style name="Theme.QuickRideCustomer.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Splash screen theme -->
    <style name="Theme.QuickRideCustomer.Splash" parent="Theme.QuickRideCustomer.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Button styles -->
    <style name="Button.Primary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/button_primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <style name="Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/button_secondary</item>
        <item name="android:textColor">@color/button_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <style name="Button.Danger" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/button_danger</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <!-- Text input styles -->
    <style name="TextInputLayout.Primary" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
        <item name="android:textColorHint">@color/gray_600</item>
    </style>

    <!-- Card styles -->
    <style name="Card.Primary" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <!-- Text styles -->
    <style name="Text.Headline" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/on_background</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text.Title" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textColor">@color/on_background</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text.Body" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/on_background</item>
    </style>

    <style name="Text.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/on_surface_variant</item>
    </style>

    <!-- Bottom navigation style -->
    <style name="BottomNavigation.Primary" parent="Widget.Material3.BottomNavigationView">
        <item name="itemIconTint">@color/primary</item>
        <item name="itemTextColor">@color/primary</item>
        <item name="android:background">@color/surface</item>
        <item name="elevation">8dp</item>
    </style>

    <!-- Toolbar style -->
    <style name="Toolbar.Primary" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/primary</item>
        <item name="titleTextColor">@color/white</item>
        <item name="subtitleTextColor">@color/white</item>
        <item name="android:theme">@style/ThemeOverlay.Material3.Dark.ActionBar</item>
    </style>

    <!-- Checkout theme for Razorpay -->
    <style name="CheckoutTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/secondary</item>
    </style>
</resources>
